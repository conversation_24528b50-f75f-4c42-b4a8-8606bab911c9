# 环境变量配置
@baseUrlForLogin = http://paydev:8080
@baseUrl = http://localhost:8080
@tenantId = K99999999
@clientId = e5cd7e4891bf95d1d19206ce24a7b32e
@grantType = password
@username = admin
@password = admin123

### 手动交易补偿evm
POST {{baseUrl}}/wallet/scan/manual/evm/BSC/tx/0x3def7c6aa96111338a9777f3c93fc072df54a6e74784634826df185dfdf16660
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 手动交易补偿tron
POST {{baseUrl}}/wallet/scan/manual/tron/tx/ab330c20577e9e885cbd4d37fc430ad09ce6545a7fd390fe614f6134b2f05d7c
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

# ========================== Solana监控管理API ==========================

### 获取Solana监控状态
GET {{baseUrl}}/api/solana/monitor/status
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 获取Solana订阅信息列表
GET {{baseUrl}}/api/solana/monitor/subscriptions
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 手动触发Solana重连
POST {{baseUrl}}/api/solana/monitor/reconnect
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

### 获取Solana监控摘要
GET {{baseUrl}}/api/solana/monitor/summary
Authorization: Bearer {{access_token}}
clientid: {{clientId}}

