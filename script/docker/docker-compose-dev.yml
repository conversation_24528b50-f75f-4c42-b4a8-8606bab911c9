services:
  pay-wallet:
    image: docker.metabank.global/kazepay/pay-wallet:2.4.0-feature-jarvey
    container_name: pay-wallet
    pull_policy: always
    environment:
      #时区上海
      TZ: Asia/Shanghai
      JAVA_OPTS: "-Xms256m -Xmx1024m -Dspring.cloud.nacos.discovery.network-interface=tailscale0 -Djava.net.preferIPv4Stack=true"
    ports:
      - "9301:9301"
    volumes:
      # 配置文件
      - /data/docker/pay-wallet/Logs/:/ruoyi/waLLet/Logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  mysql57:
    image: mysql:5.7
    container_name: mysql57-service
    pull_policy: always
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: ruoyi123
      MYSQL_DATABASE: ry-cloud
    volumes:
      - /data/docker/mysql57/data/:/var/lib/mysql/
      - /data/docker/mysql57/conf/:/etc/mysql/conf.d/
    command:
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --bind-address=0.0.0.0
    privileged: true # 建议审查是否必要
    network_mode: "host"
    restart: unless-stopped

  mysql:
    image: mysql:8.0.42
    container_name: mysql
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      # root 密码
      MYSQL_ROOT_PASSWORD: ruoyi123
      # 初始化数据库
      MYSQL_DATABASE: ry-cloud
    ports:
      - "3306:3306"
    volumes:
      # 数据挂载
      - /data/docker/mysql/data/:/var/lib/mysql/
      # 配置挂载
      - /data/docker/mysql/conf/:/etc/mysql/conf.d/
    command:
      # 将mysql8.0默认密码策略 修改为 原先 策略 (mysql8.0对其默认策略做了更改 会导致密码无法匹配)
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
    privileged: true
    network_mode: "host"

  nacos:
    image: docker.metabank.global/kazepay/ruoyi-nacos:2.4.0-rc.1
    container_name: nacos
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    pull_policy: always
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: "-Xms256m -Xmx512m"
    volumes:
      # 日志目录 注意集群模式下 日志目录不能一致 需要区分例如 nacos1 nacos2
      - /data/docker/nacos/logs/:/root/nacos/logs
      # 集群配置文件 集群所有nacos都必须使用此文件
      - /data/docker/nacos/conf/cluster.conf:/root/nacos/conf/cluster.conf
    network_mode: "host"

  redis:
    image: redis:7.2.8
    container_name: redis
    ports:
      - "6379:6379"
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    volumes:
      # 配置文件
      - /data/docker/redis/conf:/redis/config
      # 数据文件
      - /data/docker/redis/data/:/redis/data/
    command: "redis-server /redis/config/redis.conf"
    privileged: true
    network_mode: "host"

  minio:
    # minio 最后一个未阉割版本 不能再进行升级 在往上的版本功能被阉割
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z
    container_name: minio
    ports:
      # api 端口
      - "9000:9000"
      # 控制台端口
      - "9001:9001"
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      # 管理后台用户名
      MINIO_ROOT_USER: ruoyi
      # 管理后台密码，最小8个字符
      MINIO_ROOT_PASSWORD: ruoyi123
      # https需要指定域名
      #MINIO_SERVER_URL: "https://xxx.com:9000"
      #MINIO_BROWSER_REDIRECT_URL: "https://xxx.com:9001"
      # 开启压缩 on 开启 off 关闭
      MINIO_COMPRESS: "off"
      # 扩展名 .pdf,.doc 为空 所有类型均压缩
      MINIO_COMPRESS_EXTENSIONS: ""
      # mime 类型 application/pdf 为空 所有类型均压缩
      MINIO_COMPRESS_MIME_TYPES: ""
    volumes:
      # 映射当前目录下的data目录至容器内/data目录
      - /data/docker/minio/data:/data
      # 映射配置目录
      - /data/docker/minio/config:/root/.minio/
    command: server --address ':9000' --console-address ':9001' /data  # 指定容器中的目录 /data
    privileged: true
    network_mode: "host"

  seata-server:
    image: docker.metabank.global/kazepay/ruoyi-seata-server:2.4.0-rc.1
    container_name: seata-server
    ports:
      - "7091:7091"
      - "8091:8091"
    pull_policy: always
    environment:
      TZ: Asia/Shanghai
      # 注意 此处ip如果是外网使用 要改为外网ip
      # SEATA_IP: 127.0.0.1
      SEATA_PORT: 8091
    volumes:
      - /data/docker/ruoyi-seata-server/logs/:/ruoyi/seata-server/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  nginx-web:
    image: nginx:1.22.1
    container_name: nginx-web
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 证书映射
      - /data/docker/nginx/cert:/etc/nginx/cert
      # 配置文件映射
      - /data/docker/nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      # 页面目录
      - /data/docker/nginx/html:/usr/share/nginx/html
      # 日志目录
      - /data/docker/nginx/log:/var/log/nginx
    privileged: true
    network_mode: "host"

  sentinel:
    image: docker.metabank.global/kazepay/ruoyi-sentinel-dashboard:2.4.0-dev.1
    container_name: sentinel
    pull_policy: always
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: "-Xms256m -Xmx512m -Dspring.cloud.nacos.discovery.network-interface=tailscale0 -Djava.net.preferIPv4Stack=true "
    ports:
      - "8718:8718"
    volumes:
      # 配置文件
      - /data/docker/ruoyi-sentinel-dashboard/logs/:/ruoyi/sentinel-dashboard/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    restart: always
    network_mode: "host"

  ruoyi-monitor:
    image: docker.metabank.global/kazepay/ruoyi-monitor:2.4.0-rc.1
    container_name: ruoyi-monitor
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "9100:9100"
    volumes:
      # 配置文件
      - /data/docker/ruoyi-monitor/logs/:/ruoyi/monitor/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  ruoyi-snailjob-server:
    image: docker.metabank.global/kazepay/ruoyi-snailjob-server:2.4.0-rc.1
    container_name: ruoyi-snailjob-server
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "8800:8800"
      - "17888:17888"
    volumes:
      - /data/docker/snailjob/logs/:/ruoyi/snailjob/logs
    privileged: true
    network_mode: "host"

  ruoyi-gateway:
    image: docker.metabank.global/kazepay/ruoyi-gateway:2.4.0-rc.1
    container_name: ruoyi-gateway
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "8080:8080"
    volumes:
      # 配置文件
      - /data/docker/ruoyi-gateway/logs/:/ruoyi/gateway/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  ruoyi-auth:
    image: docker.metabank.global/kazepay/ruoyi-auth:2.4.0-feature-jarvey
    container_name: ruoyi-auth
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "9210:9210"
    volumes:
      # 配置文件
      - /data/docker/ruoyi-auth/logs/:/ruoyi/auth/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  ruoyi-system:
    image: docker.metabank.global/kazepay/ruoyi-system:2.4.0-feature-jarvey
    container_name: ruoyi-system
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      JAVA_OPTS: "-Xms256m -Xmx1024m -Dspring.cloud.nacos.discovery.network-interface=tailscale0 -Djava.net.preferIPv4Stack=true"
    ports:
      - "9201:9201"
    volumes:
      # 配置文件
      - /data/docker/ruoyi-system/logs/:/ruoyi/system/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  ruoyi-gen:
    image: docker.metabank.global/kazepay/ruoyi-gen:2.4.0-rc.1
    container_name: ruoyi-gen
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "9202:9202"
    volumes:
      # 配置文件
      - /data/docker/ruoyi-gen/logs/:/ruoyi/gen/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  ruoyi-job:
    image: docker.metabank.global/kazepay/ruoyi-job:2.4.0-rc.1
    container_name: ruoyi-job
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      SERVER_PORT: 9203
      SNAIL_PORT: 19203
    ports:
      - "9203:9203"
    volumes:
      # 配置文件
      - /data/docker/ruoyi-job/logs/:/ruoyi/job/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  ruoyi-resource:
    image: docker.metabank.global/kazepay/ruoyi-resource:2.4.0-rc.1
    container_name: ruoyi-resource
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "9204:9204"
    volumes:
      # 配置文件
      - /data/docker/ruoyi-resource/logs/:/ruoyi/resource/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  ruoyi-workflow:
    image: docker.metabank.global/kazepay/ruoyi-workflow:2.4.0-rc.1
    container_name: ruoyi-workflow
    pull_policy: always
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "9205:9205"
    volumes:
      # 配置文件
      - /data/docker/ruoyi-workflow/logs/:/ruoyi/workflow/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  pay-acct:
    image: kazepay/pay-acct:latest
    container_name: pay-acct
    pull_policy: always
    environment:
      JAVA_OPTS: "-Xms256m -Xmx1024m -Dspring.cloud.nacos.discovery.network-interface=tailscale0 -Djava.net.preferIPv4Stack=true"
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "9206:9206"
    volumes:
      # 配置文件
      - /data/docker/pay-acct/logs/:/ruoyi/acct/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  pay-aset:
    image: docker.metabank.global/kazepay/pay-aset:2.4.0-feature-jarvey
    container_name: pay-aset
    pull_policy: always
    environment:
      JAVA_OPTS: "-Xms256m -Xmx1024m -Dspring.cloud.nacos.discovery.network-interface=tailscale0 -Djava.net.preferIPv4Stack=true"
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "9207:9207"
    volumes:
      # 配置文件
      - /data/docker/pay-aset/logs/:/ruoyi/aset/logs
      # skywalking 探针
      - /data/docker/skywalking/agent/:/ruoyi/skywalking/agent
    privileged: true
    network_mode: "host"

  #################################################################################################
  #################################### 以下为扩展根据需求搭建 #########################################
  #################################################################################################

  elasticsearch:
    image: elasticsearch:7.17.6
    container_name: elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    pull_policy: always
    environment:
      # 设置集群名称
      cluster.name: elasticsearch
      # 以单一节点模式启动
      discovery.type: single-node
      ES_JAVA_OPTS: "-Xms512m -Xmx512m"
    volumes:
      - /data/docker/elk/elasticsearch/plugins:/usr/share/elasticsearch/plugins
      - /data/docker/elk/elasticsearch/data:/usr/share/elasticsearch/data
      - /data/docker/elk/elasticsearch/logs:/usr/share/elasticsearch/logs
    network_mode: "host"

  kibana:
    image: kibana:7.17.6
    container_name: kibana
    ports:
      - "5601:5601"
    depends_on:
      # kibana在elasticsearch启动之后再启动
      - elasticsearch
    pull_policy: always
    environment:
      #设置系统语言文中文
      I18N_LOCALE: zh-CN
      # 访问域名
      # SERVER_PUBLICBASEURL: https://kibana.cloud.com
    volumes:
      - /data/docker/elk/kibana/config/kibana.yml:/usr/share/kibana/config/kibana.yml
    network_mode: "host"

  logstash:
    image: logstash:7.17.6
    container_name: logstash
    ports:
      - "4560:4560"
    volumes:
      - /data/docker/elk/logstash/pipeline/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - /data/docker/elk/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml
    depends_on:
      - elasticsearch
    network_mode: "host"

  rmqnamesrv:
    image: apache/rocketmq:5.2.0
    container_name: rmqnamesrv
    ports:
      - "9876:9876"
    pull_policy: always
    environment:
      JAVA_OPT: -server -Xms512m -Xmx512m
    command: sh mqnamesrv
    volumes:
      - /data/docker/rocketmq/namesrv/logs:/home/<USER>/logs/rocketmqlogs
    network_mode: "host"

  rmqbroker1:
    image: apache/rocketmq:5.2.0
    container_name: rmqbroker1
    ports:
      - "10911:10911"
      - "10909:10909"
      - "10912:10912"
    pull_policy: always
    environment:
      JAVA_OPT: -server -Xms512M -Xmx512M
      NAMESRV_ADDR: 127.0.0.1:9876
    # --enable-proxy 开启broker与proxy共用模式 生产部署建议将proxy单独部署
    command: sh mqbroker --enable-proxy -c /home/<USER>/rocketmq-5.2.0/conf/broker.conf
    depends_on:
      - rmqnamesrv
    volumes:
      - /data/docker/rocketmq/broker1/conf/broker.conf:/home/<USER>/rocketmq-5.2.0/conf/broker.conf
      - /data/docker/rocketmq/broker1/logs:/home/<USER>/logs/rocketmqlogs
      - /data/docker/rocketmq/broker1/store:/home/<USER>/store
    privileged: true
    network_mode: "host"

  rmqconsole:
    image: apacherocketmq/rocketmq-dashboard:latest
    container_name: rmqconsole
    ports:
      - "19876:19876"
    pull_policy: always
    environment:
      JAVA_OPTS: -Dserver.port=19876 -Drocketmq.namesrv.addr=127.0.0.1:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    depends_on:
      - rmqnamesrv
    network_mode: "host"

  rabbitmq:
    container_name: rabbitmq
    build:
      context: ./rabbitmq
    pull_policy: always
    environment:
      RABBITMQ_DEFAULT_USER: ruoyi
      RABBITMQ_DEFAULT_PASS: ruoyi123
    ports:
      - "15672:15672" # 管理界面端口
      - "5672:5672"   # api 端口
    volumes:
      - /data/docker/rabbitmq/log:/var/log/rabbitmq
      - /data/docker/rabbitmq/data:/var/lib/rabbitmq
    network_mode: "host"

  zookeeper:
    image: 'bitnami/zookeeper:3.8.0'
    container_name: zookeeper
    ports:
      - "2181:2181"
    pull_policy: always
    environment:
      TZ: Asia/Shanghai
      ALLOW_ANONYMOUS_LOGIN: "yes"
      ZOO_SERVER_ID: 1
      ZOO_PORT_NUMBER: 2181
      # 自带的控制台 一般用不上可自行开启
      ZOO_ENABLE_ADMIN_SERVER: "no"
      # 自带控制台的端口
      ZOO_ADMIN_SERVER_PORT_NUMBER: 8080
    network_mode: "host"

  kafka:
    image: 'bitnami/kafka:3.6.2'
    container_name: kafka
    ports:
      - "9092:9092"
    pull_policy: always
    environment:
      TZ: Asia/Shanghai
      # 更多变量 查看文档 https://github.com/bitnami/bitnami-docker-kafka/blob/master/README.md
      KAFKA_BROKER_ID: 1
      # 监听端口
      KAFKA_CFG_LISTENERS: PLAINTEXT://:9092
      # 实际访问ip 本地用 127 内网用 192 外网用 外网ip
      KAFKA_CFG_ADVERTISED_LISTENERS: PLAINTEXT://**************:9092
      KAFKA_CFG_ZOOKEEPER_CONNECT: 127.0.0.1:2181
      ALLOW_PLAINTEXT_LISTENER: "yes"
    volumes:
      - /data/docker/kafka/data:/bitnami/kafka/data
    depends_on:
      - zookeeper
    network_mode: "host"

  kafka-manager:
    image: sheepkiller/kafka-manager:latest
    container_name: kafka-manager
    ports:
      - "19092:19092"
    pull_policy: always
    environment:
      ZK_HOSTS: 127.0.0.1:2181
      APPLICATION_SECRET: letmein
      KAFKA_MANAGER_USERNAME: ruoyi
      KAFKA_MANAGER_PASSWORD: ruoyi123
      KM_ARGS: -Dhttp.port=19092
    depends_on:
      - kafka
    network_mode: "host"

  sky-oap:
    image: apache/skywalking-oap-server:9.7.0
    container_name: sky-oap
    ports:
      - "11800:11800"
      - "12800:12800"
    pull_policy: always
    environment:
      JAVA_OPTS: -Xms1G -Xmx2G
      #记录数据的有效期，单位天
      SW_CORE_RECORD_DATA_TTL: 7
      #分析指标数据的有效期，单位天
      SW_CORE_METRICS_DATA_TTL: 7
      SW_STORAGE: elasticsearch
      SW_STORAGE_ES_CLUSTER_NODES: 127.0.0.1:9200
      TZ: Asia/Shanghai
    network_mode: "host"

  sky-ui:
    image: apache/skywalking-ui:9.7.0
    container_name: sky-ui
    ports:
      - "18080:18080"
    pull_policy: always
    environment:
      SW_SERVER_PORT: 18080
      SW_OAP_ADDRESS: http://127.0.0.1:12800
      TZ: Asia/Shanghai
    depends_on:
      - sky-oap
    network_mode: "host"

  prometheus:
    image: prom/prometheus:v2.40.1
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - /data/docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    network_mode: "host"

  grafana:
    image: grafana/grafana:9.2.4
    container_name: grafana
    pull_policy: always
    environment:
      TZ: Asia/Shanghai
      # 服务地址 用于指定外网ip或域名
      GF_SERVER_ROOT_URL: ""
      # admin 管理员密码
      GF_SECURITY_ADMIN_PASSWORD: 123456
    ports:
      - "3000:3000"
    volumes:
      - /data/docker/grafana/grafana.ini:/etc/grafana/grafana.ini
      - /data/docker/grafana:/var/lib/grafana
    network_mode: "host"

  shardingproxy:
    image: apache/shardingsphere-proxy:5.4.0
    container_name: shardingsphere-proxy
    command: server /data
    ports:
      - "3307:3307"
    volumes:
      - /data/docker/shardingproxy/conf:/opt/shardingsphere-proxy/conf
      - /data/docker/shardingproxy/ext-lib:/opt/shardingsphere-proxy/ext-lib
    pull_policy: always
    environment:
      - JVM_OPTS="-Djava.awt.headless=true"
    network_mode: "host"
