package org.dromara.common.scanning.monitor;

import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;

import java.util.List;

/**
 * Ethereum listening events
 */
public interface EthMonitorEvent {

    /**
     * Monitor filter
     *
     * When a qualified transaction is scanned, the call method will be triggered
     * @return
     */
    default EthMonitorFilter ethMonitorFilter(){
        return null;
    }

    /**
     * Get monitor contract addresses for RPC-level filtering
     * 获取监控合约地址列表，用于RPC层面的过滤优化
     *
     * @return 监控的合约地址列表，如果返回null或空列表则不进行地址过滤
     */
    default List<String> getMonitorContractAddresses() {
        return null;
    }

    /**
     * Get monitor contract addresses for specific chain
     * 获取特定链的监控合约地址列表，用于RPC层面的过滤优化
     *
     * @param chainType 链类型标识（如：BSC、ARB、BASE等）
     * @return 监控的合约地址列表，如果返回null或空列表则不进行地址过滤
     */
    default List<String> getMonitorContractAddresses(String chainType) {
        // 默认实现：调用无参数版本以保持向后兼容
        return getMonitorContractAddresses();
    }

    /**
     * Filter the transaction data according to the above conditions, and execute the monitoring event
     * @param transactionModel
     */
    void call(TransactionModel transactionModel);
}
