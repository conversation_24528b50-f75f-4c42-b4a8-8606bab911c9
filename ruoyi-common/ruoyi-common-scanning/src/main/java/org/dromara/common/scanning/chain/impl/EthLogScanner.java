package org.dromara.common.scanning.chain.impl;

import org.dromara.common.scanning.biz.scan.ScanService;
import org.dromara.common.scanning.biz.thread.EventQueue;
import org.dromara.common.scanning.biz.thread.RetryStrategyQueue;
import org.dromara.common.scanning.biz.thread.model.EventModel;
import org.dromara.common.scanning.chain.ChainScanner;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.chain.model.eth.EthTransactionModel;
import org.dromara.common.scanning.commons.config.BatchConfig;
import org.dromara.common.scanning.commons.config.BlockChainConfig;
import org.dromara.common.scanning.commons.enums.BlockEnums;
import org.dromara.common.scanning.monitor.EthMonitorEvent;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.response.EthLog;
import org.web3j.protocol.core.methods.response.Log;
import org.web3j.protocol.http.HttpService;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

/**
 * 以太坊日志批量扫描器
 * 使用eth_getLogs批量获取事件日志，提升扫描效率
 */
public class EthLogScanner extends ChainScanner {

    private Logger logger = LoggerFactory.getLogger(EthLogScanner.class);

    /**
     * Web3j客户端列表
     */
    private List<Web3j> web3jList;

    /**
     * 批量配置
     */
    private BatchConfig batchConfig;

    /**
     * 当前RPC节点索引
     */
    private int currentRpcIndex = 0;

    /**
     * ETH监控事件列表
     */
    private List<EthMonitorEvent> ethMonitorEventList;

    /**
     * 链类型标识（用于日志显示）
     */
    private String chainType = "ETH";

    /**
     * 默认构造函数（用于ChainScanner架构）
     */
    public EthLogScanner() {
        super();
    }

    /**
     * 兼容性构造函数（保持向后兼容）
     */
    public EthLogScanner(List<Web3j> web3jList, BatchConfig batchConfig, EventQueue eventQueue) {
        this.web3jList = web3jList;
        this.batchConfig = batchConfig;
        this.eventQueue = eventQueue;
    }

    /**
     * 初始化方法（ChainScanner架构要求）
     */
    @Override
    public void init(BlockChainConfig blockChainConfig, EventQueue eventQueue, RetryStrategyQueue retryStrategyQueue, ScanService scanService) {
        super.init(blockChainConfig, eventQueue, retryStrategyQueue, scanService);

        // 设置链类型标识 - 优先级：customChainName > ChainType.getChainName() > 默认值"ETH"
        if (blockChainConfig.getCustomChainName() != null && !blockChainConfig.getCustomChainName().trim().isEmpty()) {
            this.chainType = blockChainConfig.getCustomChainName().trim().toUpperCase();
            logger.info("[{}-LOG] Using custom chain name: {}", chainType, chainType);
        } else if (blockChainConfig.getChainType() != null) {
            this.chainType = blockChainConfig.getChainType().getChainName();
            logger.info("[{}-LOG] Using ChainType enum name: {}", chainType, chainType);
        } else {
            logger.warn("[{}-LOG] No chain type configured, using default: ETH", chainType);
        }

        this.ethMonitorEventList = blockChainConfig.getEventConfig().getEthMonitorEvent();
        this.batchConfig = blockChainConfig.getBatchConfig();
        if (this.batchConfig == null) {
            this.batchConfig = BatchConfig.getDefault();
        }

        this.web3jList = new ArrayList<>();
        for (HttpService httpService : blockChainConfig.getHttpService()) {
            this.web3jList.add(Web3j.build(httpService));
        }

        logger.info("[{}-LOG] EthLogScanner initialized with optimal range scanning mode", chainType);
    }

    /**
     * 扫描方法（ChainScanner架构要求）
     */
    @Override
    public void scan(BigInteger beginBlockNumber) {
        try {
            Web3j web3j = this.web3jList.get(getNextIndex(web3jList.size()));

            // 获取最新区块号，添加错误处理
            BigInteger lastBlockNumber;
            try {
                lastBlockNumber = web3j.ethBlockNumber().send().getBlockNumber();
            } catch (Exception e) {
                logger.error("[{}-LOG], Failed to get latest block number: {}", chainType, e.getMessage());
                // 如果获取失败，使用当前扫描高度继续
                if (scanService.getCurrentBlockHeight() != null) {
                    lastBlockNumber = scanService.getCurrentBlockHeight();
                } else {
                    logger.error("[{}-LOG], No fallback block height available, skipping scan", chainType);
                    return;
                }
            }

            if (beginBlockNumber.compareTo(BlockEnums.LAST_BLOCK_NUMBER.getValue()) == 0) {
                beginBlockNumber = lastBlockNumber;
            }

            if (scanService.getCurrentBlockHeight() == null) {
                scanService.setCurrentBlockHeight(lastBlockNumber);
            }

            if (beginBlockNumber.compareTo(lastBlockNumber) > 0) {
                logger.info("[{}-LOG], Scan progress ahead of chain, waiting... scan: {}, chain: {}",
                    chainType, beginBlockNumber, lastBlockNumber);
                return;
            }

            // 检查结束区块限制
            if (blockChainConfig.getEndBlockNumber().compareTo(BigInteger.ZERO) > 0
                && beginBlockNumber.compareTo(blockChainConfig.getEndBlockNumber()) >= 0) {
                logger.info("[{}-LOG], Reached end block {}, stopping scan", chainType, blockChainConfig.getEndBlockNumber());
                scanService.shutdown();
                return;
            }

            // 计算最优扫描范围
            BigInteger endBlockNumber = calculateOptimalEndBlock(beginBlockNumber, lastBlockNumber);

            logger.debug("[{}-LOG], Scanning blocks from {} to {}", chainType, beginBlockNumber, endBlockNumber);

            // 直接获取日志（无并发分片）
            List<Log> logs = fetchLogsDirectly(beginBlockNumber, endBlockNumber);

            // 处理日志
            if (!logs.isEmpty()) {
                processLogsToEventQueue(logs);
            }

            // 更新扫描进度
            blockChainConfig.setBeginBlockNumber(endBlockNumber.add(BigInteger.ONE));
            scanService.setCurrentBlockHeight(endBlockNumber);

            logger.info("[{}-LOG] Scanned blocks {}-{}, found {} logs",
                       chainType, beginBlockNumber, endBlockNumber, logs.size());

        } catch (Exception e) {
            logger.error("[{}-LOG], Exception occurred during scanning, block: {}", chainType, beginBlockNumber, e);
            // 添加重试机制，与其他扫描器保持一致
            addRetry(beginBlockNumber);
        }
    }

    /**
     * 处理监控事件（ChainScanner架构要求）
     */
    @Override
    public void call(TransactionModel transactionModel) {
        if (ethMonitorEventList == null || ethMonitorEventList.isEmpty()) {
            return;
        }

        for (EthMonitorEvent ethMonitorEvent : ethMonitorEventList) {
            try {
                ethMonitorEvent.call(transactionModel);
            } catch (Exception e) {
                logger.error("[{}-LOG], Exception in monitor event", chainType, e);
            }
        }
    }

    /**
     * 从监控事件中提取合约地址
     * 用于底层RPC查询的地址过滤
     */
    private List<String> extractMonitorContractAddresses() {
        List<String> contractAddresses = new ArrayList<>();

        if (ethMonitorEventList != null && !ethMonitorEventList.isEmpty()) {
            for (EthMonitorEvent ethMonitorEvent : ethMonitorEventList) {
                try {
                    // 直接调用getMonitorContractAddresses方法
                    List<String> eventContractAddresses = ethMonitorEvent.getMonitorContractAddresses();

                    if (eventContractAddresses != null && !eventContractAddresses.isEmpty()) {
                        for (String address : eventContractAddresses) {
                            if (address != null && !contractAddresses.contains(address.toLowerCase())) {
                                contractAddresses.add(address.toLowerCase());
                            }
                        }
                    } else {
                        // 降级到从EthMonitorFilter中提取toAddress
                        EthMonitorFilter filter = ethMonitorEvent.ethMonitorFilter();
                        if (filter != null && filter.getToAddress() != null) {
                            String toAddress = filter.getToAddress().toLowerCase();
                            if (!contractAddresses.contains(toAddress)) {
                                contractAddresses.add(toAddress);
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.warn("[{}-LOG], Failed to extract contract address from monitor event: {}", chainType, e.getMessage());
                }
            }
        }

        if (!contractAddresses.isEmpty()) {
            logger.info("[{}-LOG], Extracted {} monitor contract addresses for RPC filtering: {}",
                chainType, contractAddresses.size(), contractAddresses);
        } else {
            logger.debug("[{}-LOG], No monitor contract addresses found, will scan all contracts", chainType);
        }

        return contractAddresses;
    }

    /**
     * 计算最优扫描范围
     */
    private BigInteger calculateOptimalEndBlock(BigInteger beginBlock, BigInteger latestBlock) {
        // 根据监控事件数量动态调整范围
        int eventCount = ethMonitorEventList != null ? ethMonitorEventList.size() : 1;

        long rangeSize;
        if (eventCount <= 2) {
            rangeSize = 10000;  // 少量事件，大范围扫描
        } else if (eventCount <= 5) {
            rangeSize = 5000;   // 中等数量
        } else {
            rangeSize = 2000;   // 大量事件，小范围扫描
        }

        BigInteger endBlock = beginBlock.add(BigInteger.valueOf(rangeSize));

        // 不超过最新区块
        if (endBlock.compareTo(latestBlock) > 0) {
            endBlock = latestBlock;
        }

        // 不超过配置的结束区块
        if (blockChainConfig.getEndBlockNumber().compareTo(BigInteger.ZERO) > 0
            && endBlock.compareTo(blockChainConfig.getEndBlockNumber()) > 0) {
            endBlock = blockChainConfig.getEndBlockNumber();
        }

        return endBlock;
    }

    /**
     * 直接获取指定区块范围的日志
     */
    private List<Log> fetchLogsDirectly(BigInteger fromBlock, BigInteger toBlock) throws Exception {
        Web3j web3j = getNextWeb3j();

        // 提取监控地址
        List<String> monitorAddresses = extractMonitorContractAddresses();

        // 创建优化的过滤器
        EthFilter filter = createOptimizedFilter(fromBlock, toBlock, monitorAddresses);

        // 执行查询
        EthLog ethLog = web3j.ethGetLogs(filter).send();

        if (ethLog.hasError()) {
            throw new RuntimeException("eth_getLogs error: " + ethLog.getError().getMessage());
        }

        // 转换结果
        List<Log> logs = new ArrayList<>();
        for (EthLog.LogResult logResult : ethLog.getResult()) {
            if (logResult instanceof EthLog.LogObject) {
                logs.add(((EthLog.LogObject) logResult).get());
            }
        }

        logger.debug("[{}-LOG] Fetched {} logs from blocks {}-{}",
                    chainType, logs.size(), fromBlock, toBlock);

        return logs;
    }

    /**
     * 创建优化的过滤器
     */
    private EthFilter createOptimizedFilter(BigInteger fromBlock, BigInteger toBlock, List<String> addresses) {
        EthFilter filter = new EthFilter(
            DefaultBlockParameter.valueOf(fromBlock),
            DefaultBlockParameter.valueOf(toBlock),
            addresses
        );

        // 添加常见事件主题过滤
        addRelevantTopics(filter);

        return filter;
    }

    /**
     * 添加相关事件主题过滤
     */
    private void addRelevantTopics(EthFilter filter) {
        List<String> topics = new ArrayList<>();

        // 常见ERC20事件
        topics.add("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"); // Transfer
//        topics.add("0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925"); // Approval

        if (!topics.isEmpty()) {
            filter.addOptionalTopics(topics.toArray(new String[0]));
            logger.debug("[{}-LOG] Applied topic filter: {}", chainType, topics);
        }
    }

    /**
     * 处理日志并推送到事件队列
     */
    private void processLogsToEventQueue(List<Log> logs) {
        if (logs == null || logs.isEmpty()) {
            return;
        }

        List<TransactionModel> transactionModels = new ArrayList<>();

        for (Log log : logs) {
            TransactionModel transactionModel = TransactionModel.builder()
                .setEthTransactionModel(EthTransactionModel.fromLog(log))
                .setChainType(chainType);
            transactionModels.add(transactionModel);
        }

        // 创建EventModel推送到队列
        EventModel eventModel = EventModel.builder()
            .setCurrentBlockHeight(logs.get(0).getBlockNumber())
            .setTransactionModels(transactionModels);

        eventQueue.add(eventModel);

        logger.debug("[{}-LOG] Processed {} logs to event queue", chainType, logs.size());
    }

    /**
     * 获取下一个Web3j客户端（负载均衡）
     */
    private Web3j getNextWeb3j() {
        if (web3jList.isEmpty()) {
            throw new IllegalStateException("No Web3j clients available");
        }

        Web3j web3j = web3jList.get(currentRpcIndex);
        currentRpcIndex = (currentRpcIndex + 1) % web3jList.size();
        return web3j;
    }

    /**
     * 关闭资源
     */
    public void shutdown() {
        logger.info("[{}-LOG] Starting shutdown process...", chainType);

        // 关闭Web3j客户端连接
        if (web3jList != null && !web3jList.isEmpty()) {
            for (Web3j web3j : web3jList) {
                try {
                    web3j.shutdown();
                    logger.debug("[{}-LOG] Web3j client shutdown completed", chainType);
                } catch (Exception e) {
                    logger.error("[{}-LOG] Error shutting down Web3j client", chainType, e);
                }
            }
            web3jList.clear();
        }

        logger.info("[{}-LOG] Shutdown process completed", chainType);
    }


}
