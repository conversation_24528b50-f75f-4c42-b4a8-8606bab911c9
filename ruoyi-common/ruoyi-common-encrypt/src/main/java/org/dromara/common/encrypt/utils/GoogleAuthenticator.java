package org.dromara.common.encrypt.utils;

import org.apache.commons.codec.binary.Base32;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

public class GoogleAuthenticator {
    /**
     * ⽣成秘钥的⻓度
     */
    public static final int SECRET_SIZE = 2;
    public static final String SEED =  "g8GjEvTbW5oVSV7avL47357438reyhreyuryetredLDVKs2m0QN7vxRs2im5MDaNCWGmcD2rvcZx";
    /**
     * 实现随机数算法
     */
    public static final String RANDOM_NUMBER_ALGORITHM = "SHA1PRNG";
    /**
     * 时间前后偏移量
     * ⽤于防⽌客户端时间不精确导致⽣成的TOTP与服务器端的TOTP⼀直不⼀致
     * 如果为0,当前时间为 10:10:15
     * 则表明在 10:10:00-10:10:30 之间⽣成的TOTP 能校验通过
     * 如果为1,则表明在
     * 10:09:30-10:10:00
     * 10:10:00-10:10:30
     * 10:10:30-10:11:00 之间⽣成的TOTP 能校验通过
     * 以此类推
     */
    static int window_size = 1; // default 3 - max 17
    /**
     * set the windows size. This is an integer value representing the
     number of
     * 30 second windows we allow The bigger the window, the more
     tolerant of
     * clock skew we are.
     *
     * @param s
     * window size - must be >=1 and <=17. Other values are ignored
     */
    public void setWindowSize(int s) {
        if (s >= 1 && s <= 17)
            window_size = s;
    }
    /**
     * ⽣成随机密钥，每个⽤户独享⼀份密钥
     * @return secret key
     */
    public static String generateSecretKey() {
        SecureRandom sr;
        try {
            sr = SecureRandom.getInstance(RANDOM_NUMBER_ALGORITHM);
            sr.setSeed(Base64.decodeBase64(SEED.getBytes()));
            byte[] buffer = sr.generateSeed(SECRET_SIZE);
            Base32 codec = new Base32();
            byte[] bEncodedKey = codec.encode(buffer);
            return new String(bEncodedKey);
        } catch (NoSuchAlgorithmException e) {
            // should never occur... configuration error
        }
        return null;
    }
    /**
     * ⽣成⼀个google身份验证器，识别的字符串，只需要把该⽅法返回值⽣成⼆维码扫描就
     可以了。
     * 最后展示的账户名称将会是 label:user
     * @param label 标签
     * @param user 账号
     * @param secret 密钥
     */
    public static String getQRBarcode(String label, String user, String
            secret) {
        String format = "otpauth://totp/%s:%s?secret=%s";
        return String.format(format, label, user, secret);
    }
    /**
     * ⽣成⼀个google身份验证器，识别的字符串，只需要把该⽅法返回值⽣成⼆维码扫描就
     可以了。
     *最后展示的账户名称将会是 user
     * @param user 账号
     * @param secret 密钥
     */
    public static String getQRBarcode(String user, String secret) {
        String format = "otpauth://totp/%s?secret=%s";
        return String.format(format, user, secret);
    }
    /**
     * 验证code是否合法
     * @param secret 秘钥
     * @param code 验证码
     * @return true表示正确 false 表示错误
     */
    public static boolean checkCode(String secret, long code)
    {
        long timeMses = System.currentTimeMillis();
        if(secret == null || secret.isEmpty())
        {
            return false;
        }
        Base32 codec = new Base32();
        byte[] decodedKey = codec.decode(secret);
        // convert unix msec time into a 30 second "window"
        // this is per the TOTP spec (see the RFC for details)
        long t = (timeMses / 1000L) / 30L;
        // Window is used to check codes generated in the near past.
        // You can use this value to tune how far you're willing to go.
        for (int i = -window_size; i <= window_size; ++i) {
            long hash;
            try {
                hash = verify_code(decodedKey, t + i);
            } catch (Exception e) {
                // Yes, this is bad form - but
                // the exceptions thrown would be rare and a static
                // configuration problem
                e.printStackTrace();
                throw new RuntimeException(e.getMessage());
                // return false;
            }
            if (hash == code) {
                return true;
            }
        }
        // The validation code is invalid.
        return false;
    }
    /**
     * 根据时间偏移量计算
     *
     */
    private static int verify_code(byte[] key, long t) throws
            NoSuchAlgorithmException, InvalidKeyException {
        byte[] data = new byte[8];
        long value = t;
        for (int i = 8; i-- > 0; value >>>= 8) {
            data[i] = (byte) value;
        }
        SecretKeySpec signKey = new SecretKeySpec(key, "HmacSHA1");
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(signKey);
        byte[] hash = mac.doFinal(data);
        int offset = hash[20 - 1] & 0xF;
        // We're using a long because Java hasn't got unsigned int.
        long truncatedHash = 0;
        for (int i = 0; i < 4; ++i) {
            truncatedHash <<= 8;
            // We are dealing with signed bytes:
            // we just keep the first byte.
            truncatedHash |= (hash[offset + i] & 0xFF);
        }
        truncatedHash &= 0x7FFFFFFF;
        truncatedHash %= 1000000;
        return (int) truncatedHash;
    }

//    public static void main(String[] args) {
//        String secretKey = GoogleAuthenticator.generateSecretKey();
//        System.out.println("秘钥："+secretKey);
//    }
}
