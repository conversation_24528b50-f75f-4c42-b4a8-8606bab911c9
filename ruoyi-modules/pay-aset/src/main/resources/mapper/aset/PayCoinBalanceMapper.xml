<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.aset.mapper.PayCoinBalanceMapper">

    <resultMap type="org.dromara.aset.domain.vo.PayCoinBalanceVo" id="payCoinBalanceResult">
    </resultMap>

    <update id="processRefundFromFrozenFunds"
            parameterType="org.dromara.aset.domain.bo.PayCoinBalanceBo">
        update k_meta_coin_balance
        set freeze_balance = freeze_balance - #{bo.amount}
          , coin_balance   = coin_balance + #{amountChange}
        where user_id = #{bo.userId}
          and coin_code = #{bo.coinCode}
          and freeze_balance >= #{bo.amount}
    </update>


    <!-- 7个不同表，需要基于tenant_id选择 -->
    <update id="processRefundFromFrozenFundsForMeta">
        update kmeta_meta_coin_balance
        set freeze_balance = freeze_balance - #{amountChange}
            , coin_balance = coin_balance + #{amountChange}
        where user_id = #{userId}
          and coin_code = #{coinCode}
          and freeze_balance >= #{amountChange}
    </update>

    <update id="processRefundFromFrozenFundsForBitago">
        update kazepay.kbitago_meta_coin_balance
        set freeze_balance = freeze_balance - #{amountChange}
          , coin_balance = coin_balance + #{amountChange}
        where user_id = #{userId}
          and coin_code = #{coinCode}
          and freeze_balance >= #{amountChange}
    </update>
    <update id="processRefundFromFrozenFundsForRokutel">
        update kazepay.krokutel_meta_coin_balance
        set freeze_balance = freeze_balance - #{amountChange}
          , coin_balance = coin_balance + #{amountChange}
        where user_id = #{userId}
          and coin_code = #{coinCode}
          and freeze_balance >= #{amountChange}
    </update>
    <update id="processRefundFromFrozenFundsForZombie">
        update kazepay.kzombie_meta_coin_balance
        set freeze_balance = freeze_balance - #{amountChange}
          , coin_balance = coin_balance + #{amountChange}
        where user_id = #{userId}
          and coin_code = #{coinCode}
          and freeze_balance >= #{amountChange}
    </update>
    <update id="processRefundFromFrozenFundsForXcode">
        update kazepay.kxcode_meta_coin_balance
        set freeze_balance = freeze_balance - #{amountChange}
          , coin_balance = coin_balance + #{amountChange}
        where user_id = #{userId}
          and coin_code = #{coinCode}
          and freeze_balance >= #{amountChange}
    </update>
    <update id="processRefundFromFrozenFundsForVoopay">
        update kazepay.kvoopay_meta_coin_balance
        set freeze_balance = freeze_balance - #{amountChange}
          , coin_balance = coin_balance + #{amountChange}
        where user_id = #{userId}
          and coin_code = #{coinCode}
          and freeze_balance >= #{amountChange}
    </update>
    <update id="processRefundFromFrozenFundsForSwipex">
        update kazepay.kswipex_meta_coin_balance
        set freeze_balance = freeze_balance - #{amountChange}
          , coin_balance   = coin_balance + #{amountChange}
        where user_id = #{userId}
          and coin_code = #{coinCode}
          and freeze_balance >= #{amountChange}
    </update>


</mapper>
