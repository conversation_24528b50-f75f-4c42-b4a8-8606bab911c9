package org.dromara.aset.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.aset.domain.bo.PayCoinTxnDtlBo;
import org.dromara.aset.domain.bo.PayTxnDtlCodeBo;
import org.dromara.aset.domain.vo.BatchWithdrawalResult;
import org.dromara.aset.domain.vo.PayCoinTxnDtlVo;
import org.dromara.aset.domain.vo.PayTxnDtlCodeVo;
import org.dromara.aset.service.IPayCoinTxnDtlService;
import org.dromara.aset.service.PayCoinService;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;

/**
 * 币种管理
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/payCoin")
public class PayCoinController extends BaseController {

    private final PayCoinService payCoinService;
    private final IPayCoinTxnDtlService payCoinTxnDtlService;

    /**
     * 获取币种交易列表
     */
//    @ApiEncrypt
    @PostMapping("/listForCoin")
//    @SaCheckPermission("txn:coin:list")
    public TableDataInfo<PayCoinTxnDtlVo> listForCoin(@RequestBody PayCoinTxnDtlBo bo) {
        return payCoinService.queryCoinTxnPageList(bo);
    }

    /**
     * 获取开卡币明细列表
     */
//    @ApiEncrypt
    @PostMapping("/listForCode")
//    @SaCheckPermission("txn:cardlog:list")
    public TableDataInfo<PayTxnDtlCodeVo> listForCode(@RequestBody PayTxnDtlCodeBo bo) {
        return payCoinService.queryCodeTxnPageList(bo);
    }

    /**
     * 获取提款记录列表（废弃）
     */
    @GetMapping("/listForWithdrawalsOld")
//    @SaCheckPermission("txn:coin:list")
    public TableDataInfo<PayCoinTxnDtlVo> listForWithdrawalsOld(PayCoinTxnDtlBo bo, PageQuery pageQuery) {
        return payCoinTxnDtlService.queryWithdrawalsOld(bo, pageQuery);
    }


    /**
     * 获取提款待处理记录列表
     */
    @GetMapping("/listForWithdrawals")
//    @SaCheckPermission("txn:coin:list")
    public TableDataInfo<PayCoinTxnDtlVo> listForWithdrawals(PayCoinTxnDtlBo bo, PageQuery pageQuery) {
        bo.setTxnStatus(2);
        bo.setTxnCode("c1010");
        return payCoinTxnDtlService.queryWithdrawals(bo, pageQuery);
    }

    /**
     * 获取提款待处理记录金额汇总
     */
    @GetMapping("/sumWithdrawalAmount")
//    @SaCheckPermission("txn:coin:list")
    public R<BigDecimal> sumWithdrawalAmount() {
        return R.ok(payCoinTxnDtlService.sumWithdrawalAmount());
    }

    /**
     * 安全提款接口 - 根据txnId执行提款
     * 前端只需传输txnId，后端查询数据库获取完整提款信息后执行
     *
     * @param txnId 交易ID
     * @return 提款执行结果
     */
    @PostMapping("/executeWithdrawal/{txnId}")
//    @SaCheckPermission("txn:coin:withdraw")
    public R<String> executeWithdrawal(@PathVariable Long txnId) {
        try {
            String result = payCoinTxnDtlService.executeWithdrawal(txnId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("执行提款失败，txnId: {}, error: {}", txnId, e.getMessage());
            return R.fail("提款执行失败: " + e.getMessage());
        }
    }

    /**
     * 批量安全提款接口 - 根据txnId列表执行批量提款
     * 前端只需传输txnId列表，后端查询数据库获取完整提款信息后执行批量提款
     *
     * @param txnIds 交易ID列表
     * @return 批量提款执行结果
     */
    @PostMapping("/executeBatchWithdrawal")
//    @SaCheckPermission("txn:coin:withdraw")
    public R<BatchWithdrawalResult> executeBatchWithdrawal(@RequestBody List<Long> txnIds) {
        try {
            BatchWithdrawalResult result = payCoinTxnDtlService.executeBatchWithdrawal(txnIds);
            return R.ok(result);
        } catch (Exception e) {
            log.error("执行批量提款失败，txnIds: {}, error: {}", txnIds, e.getMessage());
            return R.fail("批量提款执行失败: " + e.getMessage());
        }
    }

}
