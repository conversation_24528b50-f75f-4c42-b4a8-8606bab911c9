package org.dromara.wallet.wallet.transfer.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 交易确认配置
 *
 * <p>用于配置不同区块链的交易确认参数，包括：</p>
 * <ul>
 *   <li>确认超时时间</li>
 *   <li>检查间隔</li>
 *   <li>重试次数</li>
 *   <li>所需确认数</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionConfirmationConfig {

    /**
     * 确认超时时间（秒）
     * 超过此时间将停止等待确认
     */
    @Builder.Default
    private int timeoutSeconds = 60;

    /**
     * 检查间隔（秒）
     * 每次查询交易状态的间隔时间
     */
    @Builder.Default
    private int checkIntervalSeconds = 5;

    /**
     * 最大重试次数
     * 网络错误时的重试次数
     */
    @Builder.Default
    private int maxRetries = 3;

    /**
     * 所需确认数
     * 交易需要的最小确认区块数
     */
    @Builder.Default
    private int requiredConfirmations = 1;

    /**
     * 是否启用确认
     * 如果为false，将跳过确认直接返回成功
     */
    @Builder.Default
    private boolean enableConfirmation = true;

    /**
     * 确认失败是否导致转账失败
     * 如果为false，确认失败不会影响转账结果
     */
    @Builder.Default
    private boolean confirmationFailureCausesTransferFailure = false;

    /**
     * 链名称
     * 用于EVM链确认时指定具体的链配置
     * 如果为空，将使用默认的链选择逻辑
     */
    private String chainName;

    // ==================== 预定义配置 ====================

    /**
     * TRON链默认配置
     * 基于3秒区块时间，等待3个区块确认
     * 确认失败将导致转账失败，确保资金安全
     */
    public static TransactionConfirmationConfig tronDefault() {
        return TransactionConfirmationConfig.builder()
            .timeoutSeconds(30)
            .checkIntervalSeconds(3)
            .maxRetries(3)
            .requiredConfirmations(1)
            .enableConfirmation(true)
            .confirmationFailureCausesTransferFailure(true)  // 修改为true，确保交易失败时正确处理
            .chainName("TRON")
            .build();
    }

    /**
     * EVM链默认配置
     * 基于12-15秒区块时间，等待1个区块确认
     * 确认失败将导致转账失败，确保资金安全
     */
    public static TransactionConfirmationConfig evmDefault() {
        return TransactionConfirmationConfig.builder()
            .timeoutSeconds(60)
            .checkIntervalSeconds(5)
            .maxRetries(3)
            .requiredConfirmations(1)
            .enableConfirmation(true)
            .confirmationFailureCausesTransferFailure(true)  // 修改为true，确保交易失败时正确处理
            .build();
    }

    /**
     * EVM链默认配置（指定链名称）
     * 基于12-15秒区块时间，等待1个区块确认
     * 确认失败将导致转账失败，确保资金安全
     *
     * @param chainName 链名称（如：BSC、ARB、BASE、ETH）
     */
    public static TransactionConfirmationConfig evmDefault(String chainName) {
        return TransactionConfirmationConfig.builder()
            .timeoutSeconds(60)
            .checkIntervalSeconds(5)
            .maxRetries(3)
            .requiredConfirmations(1)
            .enableConfirmation(true)
            .confirmationFailureCausesTransferFailure(true)  // 修改为true，确保交易失败时正确处理
            .chainName(chainName)
            .build();
    }

    /**
     * Solana链默认配置
     * 基于400ms区块时间，但考虑网络延迟
     * 确认失败将导致转账失败，确保资金安全
     */
    public static TransactionConfirmationConfig solanaDefault() {
        return TransactionConfirmationConfig.builder()
            .timeoutSeconds(30)
            .checkIntervalSeconds(2)
            .maxRetries(5)
            .requiredConfirmations(1)
            .enableConfirmation(true)
            .confirmationFailureCausesTransferFailure(true)  // 修改为true，确保交易失败时正确处理
            .chainName("SOLANA")
            .build();
    }

    /**
     * 快速确认配置
     * 用于测试或对确认要求不高的场景
     */
    public static TransactionConfirmationConfig fast() {
        return TransactionConfirmationConfig.builder()
            .timeoutSeconds(15)
            .checkIntervalSeconds(2)
            .maxRetries(2)
            .requiredConfirmations(1)
            .enableConfirmation(true)
            .confirmationFailureCausesTransferFailure(false)
            .build();
    }

    /**
     * 禁用确认配置
     * 跳过所有确认，直接返回成功
     */
    public static TransactionConfirmationConfig disabled() {
        return TransactionConfirmationConfig.builder()
            .enableConfirmation(false)
            .build();
    }

    // ==================== 便捷方法 ====================

    /**
     * 计算总的最大等待时间（毫秒）
     */
    public long getTotalMaxWaitTimeMs() {
        return (long) timeoutSeconds * 1000;
    }

    /**
     * 计算检查间隔时间（毫秒）
     */
    public long getCheckIntervalMs() {
        return (long) checkIntervalSeconds * 1000;
    }

    /**
     * 获取配置描述
     */
    public String getDescription() {
        if (!enableConfirmation) {
            return "确认已禁用";
        }
        return String.format("超时:%ds, 间隔:%ds, 重试:%d次, 确认数:%d",
            timeoutSeconds, checkIntervalSeconds, maxRetries, requiredConfirmations);
    }

    /**
     * 验证配置参数的合理性
     */
    public void validate() {
        if (timeoutSeconds <= 0) {
            throw new IllegalArgumentException("确认超时时间必须大于0");
        }
        if (checkIntervalSeconds <= 0) {
            throw new IllegalArgumentException("检查间隔必须大于0");
        }
        if (maxRetries < 0) {
            throw new IllegalArgumentException("重试次数不能为负数");
        }
        if (requiredConfirmations < 0) {
            throw new IllegalArgumentException("所需确认数不能为负数");
        }
        if (checkIntervalSeconds >= timeoutSeconds) {
            throw new IllegalArgumentException("检查间隔不能大于等于超时时间");
        }
    }
}
