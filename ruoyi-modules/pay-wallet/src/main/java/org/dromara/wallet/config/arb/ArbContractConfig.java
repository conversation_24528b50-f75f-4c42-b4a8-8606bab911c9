package org.dromara.wallet.config.arb;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.Map;

/**
 * ARB合约配置
 * 扁平化配置 - 合约和代币相关配置
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "arb.contract")
public class ArbContractConfig {

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 代币合约配置
     */
    private Map<String, TokenDetail> contracts;



    /**
     * 代币详情配置
     */
    @Getter
    @Setter
    public static class TokenDetail {
        /**
         * 合约地址
         */
        private String address;

        /**
         * 小数位数
         */
        private int decimals = 18;

        /**
         * 代币描述
         */
        private String description;

        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * 最小转账金额
         */
        private double minAmount = 0.01;

        /**
         * 最大转账金额
         */
        private double maxAmount = 10000.0;

        /**
         * 代币符号
         */
        private String symbol;

        /**
         * 代币名称
         */
        private String name;

        /**
         * 验证配置
         */
        public void validate() {
            if (enabled) {
                if (address == null || address.trim().isEmpty()) {
                    throw new IllegalArgumentException("Token contract address cannot be empty when enabled");
                }
                if (decimals < 0 || decimals > 18) {
                    throw new IllegalArgumentException("Token decimals must be between 0 and 18");
                }
                if (minAmount < 0) {
                    throw new IllegalArgumentException("Token min amount cannot be negative");
                }
                if (maxAmount <= 0) {
                    throw new IllegalArgumentException("Token max amount must be positive");
                }
                if (minAmount >= maxAmount) {
                    throw new IllegalArgumentException("Token min amount must be less than max amount");
                }
                if (symbol == null || symbol.trim().isEmpty()) {
                    throw new IllegalArgumentException("Token symbol cannot be empty");
                }
                if (name == null || name.trim().isEmpty()) {
                    throw new IllegalArgumentException("Token name cannot be empty");
                }
            }
        }

        /**
         * 获取精度对应的除数
         */
        public BigDecimal getDecimalDivisor() {
            return BigDecimal.TEN.pow(decimals);
        }

        /**
         * 将原始金额转换为可读金额
         */
        public BigDecimal toReadableAmount(BigInteger rawAmount) {
            if (rawAmount == null) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(rawAmount).divide(getDecimalDivisor(), decimals, RoundingMode.DOWN);
        }

        /**
         * 将可读金额转换为原始金额
         */
        public BigInteger toRawAmount(BigDecimal readableAmount) {
            if (readableAmount == null) {
                return BigInteger.ZERO;
            }
            return readableAmount.multiply(getDecimalDivisor()).toBigInteger();
        }

        /**
         * 验证转账金额是否在允许范围内
         */
        public boolean isAmountValid(BigDecimal amount) {
            if (amount == null) {
                return false;
            }
            double value = amount.doubleValue();
            return value >= minAmount && value <= maxAmount;
        }
    }

    /**
     * 根据代币代码获取合约地址
     *
     * @param code 代币代码
     * @return 合约地址
     */
    public String getContractAddress(String code) {
        if (contracts == null || code == null) {
            return null;
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        return detail != null ? detail.getAddress() : null;
    }

    /**
     * 根据代币代码获取小数位数
     *
     * @param code 代币代码
     * @return 小数位数，如果未配置则默认为18
     */
    public int getContractDecimals(String code) {
        if (contracts == null || code == null) {
            return 18; // ARB默认18位小数
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        return detail != null && detail.getDecimals() > 0 ? detail.getDecimals() : 18;
    }

    /**
     * 检查代币是否启用
     *
     * @param code 代币代码
     * @return 是否启用
     */
    public boolean isTokenEnabled(String code) {
        if (contracts == null || code == null) {
            return false;
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        return detail != null && detail.isEnabled();
    }

    /**
     * 获取代币详情
     *
     * @param code 代币代码
     * @return 代币详情
     */
    public TokenDetail getTokenDetail(String code) {
        if (contracts == null || code == null) {
            return null;
        }
        return contracts.get(code.toLowerCase());
    }

    /**
     * 将原始金额转换为可读金额
     *
     * @param code 代币代码
     * @param rawAmount 原始金额
     * @return 可读金额
     */
    public BigDecimal toReadableAmount(String code, BigInteger rawAmount) {
        TokenDetail detail = getTokenDetail(code);
        if (detail == null) {
            return BigDecimal.ZERO;
        }
        return detail.toReadableAmount(rawAmount);
    }

    /**
     * 将可读金额转换为原始金额
     *
     * @param code 代币代码
     * @param readableAmount 可读金额
     * @return 原始金额
     */
    public BigInteger toRawAmount(String code, BigDecimal readableAmount) {
        TokenDetail detail = getTokenDetail(code);
        if (detail == null) {
            return BigInteger.ZERO;
        }
        return detail.toRawAmount(readableAmount);
    }

    /**
     * 验证转账金额是否在允许范围内
     *
     * @param code 代币代码
     * @param amount 转账金额
     * @return 是否有效
     */
    public boolean isAmountValid(String code, BigDecimal amount) {
        TokenDetail detail = getTokenDetail(code);
        if (detail == null) {
            return false;
        }
        return detail.isAmountValid(amount);
    }

    /**
     * 获取所有启用的代币
     */
    public Map<String, TokenDetail> getEnabledTokens() {
        if (contracts == null) {
            return Map.of();
        }
        return contracts.entrySet().stream()
                .filter(entry -> entry.getValue().isEnabled())
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));
    }

    /**
     * 验证配置
     */
    public void validate() {
        if (enabled) {
            if (contracts != null) {
                for (Map.Entry<String, TokenDetail> entry : contracts.entrySet()) {
                    String code = entry.getKey();
                    TokenDetail detail = entry.getValue();

                    if (code == null || code.trim().isEmpty()) {
                        throw new IllegalArgumentException("Token code cannot be empty");
                    }

                    if (detail == null) {
                        throw new IllegalArgumentException("Token detail cannot be null for: " + code);
                    }

                    try {
                        detail.validate();
                    } catch (Exception e) {
                        throw new IllegalArgumentException("Invalid token configuration for " + code + ": " + e.getMessage());
                    }
                }
            }
        }
    }

    /**
     * 获取配置统计信息
     */
    public String getConfigStats() {
        if (contracts == null) {
            return "No tokens configured";
        }

        long enabledCount = contracts.values().stream()
                .mapToLong(detail -> detail.isEnabled() ? 1 : 0)
                .sum();

        return String.format("Total: %d tokens, Enabled: %d", contracts.size(), enabledCount);
    }
}
