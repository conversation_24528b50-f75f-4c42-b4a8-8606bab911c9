package org.dromara.wallet.service;

import org.dromara.wallet.domain.vo.MetaArbTransactionVo;
import org.dromara.wallet.domain.bo.MetaArbTransactionBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * ARB区块高度交易明细Service接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IMetaArbTransactionService {

    /**
     * 查询ARB区块高度交易明细
     *
     * @param id 主键
     * @return ARB区块高度交易明细
     */
    MetaArbTransactionVo queryById(Long id);

    /**
     * 检查交易哈希是否已存在
     *
     * @param txid 交易哈希
     * @return 是否存在
     */
    Boolean isExist(String txid);

    /**
     * 分页查询ARB区块高度交易明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return ARB区块高度交易明细分页列表
     */
    TableDataInfo<MetaArbTransactionVo> queryPageList(MetaArbTransactionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的ARB区块高度交易明细列表
     *
     * @param bo 查询条件
     * @return ARB区块高度交易明细列表
     */
    List<MetaArbTransactionVo> queryList(MetaArbTransactionBo bo);

    /**
     * 新增ARB区块高度交易明细
     *
     * @param bo ARB区块高度交易明细
     * @return 是否新增成功
     */
    Boolean insertByBo(MetaArbTransactionBo bo);

    /**
     * 修改ARB区块高度交易明细
     *
     * @param bo ARB区块高度交易明细
     * @return 是否修改成功
     */
    Boolean updateByBo(MetaArbTransactionBo bo);

    /**
     * 校验并批量删除ARB区块高度交易明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
