package org.dromara.wallet.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.wallet.domain.bo.MetaBaseTransactionBo;
import org.dromara.wallet.domain.vo.MetaBaseTransactionVo;
import org.dromara.wallet.domain.MetaBaseTransaction;
import org.dromara.wallet.mapper.MetaBaseTransactionMapper;
import org.dromara.wallet.service.IMetaBaseTransactionService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * base区块高度交易明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MetaBaseTransactionServiceImpl implements IMetaBaseTransactionService {

    private final MetaBaseTransactionMapper baseMapper;

    /**
     * 查询base区块高度交易明细
     *
     * @param id 主键
     * @return base区块高度交易明细
     */
    @Override
    public MetaBaseTransactionVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 检查交易哈希是否已存在
     *
     * @param txid 交易哈希
     * @return 是否存在
     */
    @Override
    public Boolean isExist(String txid) {
        LambdaQueryWrapper<MetaBaseTransaction> wrapper = new LambdaQueryWrapper<MetaBaseTransaction>()
            .eq(MetaBaseTransaction::getTxid, txid);
        return baseMapper.exists(wrapper);
    }

    /**
     * 分页查询base区块高度交易明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return base区块高度交易明细分页列表
     */
    @Override
    public TableDataInfo<MetaBaseTransactionVo> queryPageList(MetaBaseTransactionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MetaBaseTransaction> lqw = buildQueryWrapper(bo);
        Page<MetaBaseTransactionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的base区块高度交易明细列表
     *
     * @param bo 查询条件
     * @return base区块高度交易明细列表
     */
    @Override
    public List<MetaBaseTransactionVo> queryList(MetaBaseTransactionBo bo) {
        LambdaQueryWrapper<MetaBaseTransaction> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MetaBaseTransaction> buildQueryWrapper(MetaBaseTransactionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MetaBaseTransaction> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MetaBaseTransaction::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTxid()), MetaBaseTransaction::getTxid, bo.getTxid());
        lqw.eq(bo.getBlockheight() != null, MetaBaseTransaction::getBlockheight, bo.getBlockheight());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), MetaBaseTransaction::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getFromaddress()), MetaBaseTransaction::getFromaddress, bo.getFromaddress());
        lqw.eq(StringUtils.isNotBlank(bo.getContract()), MetaBaseTransaction::getContract, bo.getContract());
        lqw.eq(bo.getAmount() != null, MetaBaseTransaction::getAmount, bo.getAmount());
        lqw.eq(bo.getFee() != null, MetaBaseTransaction::getFee, bo.getFee());
        lqw.eq(bo.getTimestamp() != null, MetaBaseTransaction::getTimestamp, bo.getTimestamp());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), MetaBaseTransaction::getType, bo.getType());
        lqw.eq(bo.getIssync() != null, MetaBaseTransaction::getIssync, bo.getIssync());
        return lqw;
    }

    /**
     * 新增base区块高度交易明细
     *
     * @param bo base区块高度交易明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MetaBaseTransactionBo bo) {
        MetaBaseTransaction add = MapstructUtils.convert(bo, MetaBaseTransaction.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改base区块高度交易明细
     *
     * @param bo base区块高度交易明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MetaBaseTransactionBo bo) {
        MetaBaseTransaction update = MapstructUtils.convert(bo, MetaBaseTransaction.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MetaBaseTransaction entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除base区块高度交易明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
