package org.dromara.wallet.config.bsc;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Map;

/**
 * BSC 合约配置
 * 扁平化配置 - 代币合约相关配置
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "bsc.contract")
public class BscContractConfig {

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 代币合约地址映射
     */
    private Map<String, TokenDetail> contracts;



    /**
     * 根据代币代码获取合约地址
     *
     * @param code 代币代码
     * @return 合约地址
     */
    public String getContractAddress(String code) {
        if (contracts == null || code == null) {
            return null;
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        return detail != null ? detail.getAddress() : null;
    }

    /**
     * 根据代币代码获取小数位数
     *
     * @param code 代币代码
     * @return 小数位数，如果未配置则默认为18
     */
    public int getContractDecimals(String code) {
        if (contracts == null || code == null) {
            return 18; // BSC默认18位小数
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        return detail != null && detail.getDecimals() > 0 ? detail.getDecimals() : 18;
    }

    /**
     * 根据代币代码获取最小转账金额
     *
     * @param code 代币代码
     * @return 最小转账金额
     */
    public BigDecimal getMinTransferAmount(String code) {
        if (contracts == null || code == null) {
            return new BigDecimal("0.01"); // 默认最小转账0.01个单位
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        return detail != null && detail.getMinAmount() != null ? detail.getMinAmount() : new BigDecimal("0.01");
    }

    /**
     * 根据代币代码获取最大转账金额
     *
     * @param code 代币代码
     * @return 最大转账金额
     */
    public BigDecimal getMaxTransferAmount(String code) {
        if (contracts == null || code == null) {
            return new BigDecimal("1000000"); // 默认最大转账100万
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        return detail != null && detail.getMaxAmount() != null ? detail.getMaxAmount() : new BigDecimal("1000000");
    }

    /**
     * 检查代币是否启用
     *
     * @param code 代币代码
     * @return 是否启用
     */
    public boolean isTokenEnabled(String code) {
        if (contracts == null || code == null) {
            return false;
        }
        TokenDetail detail = contracts.get(code.toLowerCase());
        return detail != null && detail.isEnabled();
    }

    /**
     * 将可读金额转换为原始金额（考虑小数位数）
     *
     * @param code 代币代码
     * @param readableAmount 可读金额
     * @return 原始金额
     */
    public BigInteger toRawAmount(String code, BigDecimal readableAmount) {
        int decimals = getContractDecimals(code);
        BigDecimal multiplier = BigDecimal.TEN.pow(decimals);
        return readableAmount.multiply(multiplier).toBigInteger();
    }

    /**
     * 将原始金额转换为可读金额（考虑小数位数）
     *
     * @param code 代币代码
     * @param rawAmount 原始金额
     * @return 可读金额
     */
    public BigDecimal toReadableAmount(String code, BigInteger rawAmount) {
        int decimals = getContractDecimals(code);
        BigDecimal divisor = BigDecimal.TEN.pow(decimals);
        return new BigDecimal(rawAmount).divide(divisor);
    }

    /**
     * 验证转账金额是否在允许范围内
     *
     * @param code 代币代码
     * @param amount 转账金额
     * @return 是否在允许范围内
     */
    public boolean isAmountValid(String code, BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        BigDecimal minAmount = getMinTransferAmount(code);
        BigDecimal maxAmount = getMaxTransferAmount(code);

        return amount.compareTo(minAmount) >= 0 && amount.compareTo(maxAmount) <= 0;
    }

    /**
     * 验证BSC地址格式
     */
    public boolean isValidBscAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }
        // BSC地址以0x开头，长度为42个字符
        return address.matches("^0x[a-fA-F0-9]{40}$");
    }

    /**
     * 代币详情配置
     */
    @Getter
    @Setter
    public static class TokenDetail {
        /**
         * 合约地址
         */
        private String address;

        /**
         * 小数位数
         */
        private int decimals = 18;

        /**
         * 代币描述
         */
        private String description;

        /**
         * 是否启用
         */
        private boolean enabled = true;

        /**
         * 最小转账金额
         */
        private BigDecimal minAmount;

        /**
         * 最大转账金额
         */
        private BigDecimal maxAmount;

        /**
         * 代币符号
         */
        private String symbol;

        /**
         * 代币名称
         */
        private String name;

        /**
         * 验证代币配置
         */
        public void validate() {
            if (enabled) {
                if (address == null || address.trim().isEmpty()) {
                    throw new IllegalArgumentException("Token contract address is required when token is enabled");
                }
                // 验证BSC地址格式
                if (!address.matches("^0x[a-fA-F0-9]{40}$")) {
                    throw new IllegalArgumentException("Invalid BSC contract address format: " + address);
                }
                if (decimals < 0 || decimals > 18) {
                    throw new IllegalArgumentException("Token decimals must be between 0 and 18");
                }
                if (minAmount != null && minAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new IllegalArgumentException("Min transfer amount must be positive");
                }
                if (maxAmount != null && maxAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new IllegalArgumentException("Max transfer amount must be positive");
                }
                if (minAmount != null && maxAmount != null && minAmount.compareTo(maxAmount) > 0) {
                    throw new IllegalArgumentException("Min transfer amount cannot be greater than max transfer amount");
                }
            }
        }
    }

    /**
     * 验证合约配置
     */
    public void validate() {
        if (enabled) {

            // 验证代币配置
            if (contracts != null) {
                for (Map.Entry<String, TokenDetail> entry : contracts.entrySet()) {
                    String code = entry.getKey();
                    TokenDetail detail = entry.getValue();

                    if (code == null || code.trim().isEmpty()) {
                        throw new IllegalArgumentException("Token code cannot be empty");
                    }

                    if (detail != null) {
                        try {
                            detail.validate();
                        } catch (Exception e) {
                            throw new IllegalArgumentException("Invalid configuration for token " + code + ": " + e.getMessage(), e);
                        }
                    }
                }
            }
        }
    }
}
