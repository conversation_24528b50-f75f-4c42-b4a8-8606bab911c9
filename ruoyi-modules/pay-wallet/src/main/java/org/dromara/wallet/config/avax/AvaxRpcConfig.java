package org.dromara.wallet.config.avax;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AVAX RPC配置
 * 扁平化配置 - RPC端点相关配置
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "avax.rpc")
public class AvaxRpcConfig {

    /**
     * 是否启用
     */
    private boolean enabled = true;

    /**
     * 网络类型：MAINNET/TESTNET
     */
    private String networkType = "MAINNET";

    /**
     * 链ID
     */
    private long chainId = 43114; // AVAX C-Chain主网

    /**
     * RPC端点（简化配置，只需要一个端点）
     */
    private String endpoint = "https://api.avax.network/ext/bc/C/rpc";

    /**
     * 连接超时时间（秒）
     */
    private int connectionTimeout = 30;

    /**
     * 读取超时时间（秒）
     */
    private int readTimeout = 60;

    /**
     * 最大重试次数
     */
    private int maxRetries = 3;

    /**
     * 重试间隔（秒）
     */
    private int retryInterval = 2;

    /**
     * 速率限制（每秒请求数）
     */
    private int rateLimit = 100;

    /**
     * 最大并发请求数
     */
    private int maxConcurrentRequests = 50;

    /**
     * 是否启用SSL验证
     */
    private boolean sslVerificationEnabled = true;

    /**
     * 是否启用请求日志
     */
    private boolean requestLoggingEnabled = false;

    /**
     * 获取可用的RPC端点（简化版本）
     */
    public String getAvailableEndpoint() {
        return endpoint;
    }

    /**
     * 获取主要端点（兼容性方法）
     */
    public String getPrimaryEndpoint() {
        return endpoint;
    }

    /**
     * 是否为主网
     */
    public boolean isMainnet() {
        return "MAINNET".equalsIgnoreCase(networkType) || chainId == 43114;
    }

    /**
     * 是否为测试网
     */
    public boolean isTestnet() {
        return "TESTNET".equalsIgnoreCase(networkType) || chainId == 43113;
    }

    /**
     * 获取网络名称
     */
    public String getNetworkName() {
        if (isMainnet()) {
            return "Avalanche C-Chain Mainnet";
        } else if (isTestnet()) {
            return "Avalanche C-Chain Testnet";
        } else {
            return "Avalanche C-Chain (Chain ID: " + chainId + ")";
        }
    }

    /**
     * 验证RPC配置
     */
    public void validate() {
        if (enabled) {
            if (networkType == null || networkType.trim().isEmpty()) {
                throw new IllegalArgumentException("Network type is required");
            }

            if (!networkType.equalsIgnoreCase("MAINNET") && !networkType.equalsIgnoreCase("TESTNET")) {
                throw new IllegalArgumentException("Network type must be MAINNET or TESTNET");
            }

            if (chainId <= 0) {
                throw new IllegalArgumentException("Chain ID must be positive");
            }

            // 验证链ID与网络类型的一致性
            if (isMainnet() && chainId != 43114) {
                log.warn("Chain ID {} does not match AVAX mainnet (43114)", chainId);
            }
            if (isTestnet() && chainId != 43113) {
                log.warn("Chain ID {} does not match AVAX testnet (43113)", chainId);
            }

            if (connectionTimeout <= 0) {
                throw new IllegalArgumentException("Connection timeout must be positive");
            }

            if (readTimeout <= 0) {
                throw new IllegalArgumentException("Read timeout must be positive");
            }

            if (maxRetries < 0) {
                throw new IllegalArgumentException("Max retries cannot be negative");
            }

            if (retryInterval <= 0) {
                throw new IllegalArgumentException("Retry interval must be positive");
            }

            if (rateLimit <= 0) {
                throw new IllegalArgumentException("Rate limit must be positive");
            }

            if (maxConcurrentRequests <= 0) {
                throw new IllegalArgumentException("Max concurrent requests must be positive");
            }
        }
    }
}
