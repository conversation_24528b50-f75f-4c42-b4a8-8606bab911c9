package org.dromara.wallet.wallet.transfer.strategy.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.base.BaseException;
import org.dromara.wallet.config.facade.SolanaConfigFacade;

import org.dromara.wallet.wallet.exception.BlockchainTransferException;
import org.dromara.wallet.wallet.helper.SolanaHelper;
import org.dromara.wallet.wallet.transfer.alert.TronTransferAlertService;
import org.dromara.wallet.wallet.transfer.config.TransactionConfirmationConfig;
import org.dromara.wallet.wallet.transfer.dto.*;
import org.dromara.wallet.wallet.transfer.dto.SolanaFeeEstimate;
import org.dromara.wallet.wallet.transfer.exception.SolanaTransferException;
import org.dromara.wallet.wallet.transfer.monitor.SolanaTransferMetrics;
import org.dromara.wallet.wallet.transfer.strategy.AbstractTransferStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * Solana转账策略实现 - 完整迁移版本
 *
 * <p>封装Solana链的转账逻辑，支持以下特性：</p>
 * <ul>
 *   <li>SOL原生代币转账</li>
 *   <li>SPL合约代币转账</li>
 *   <li>手续费钱包支持</li>
 *   <li>交易确认和验证</li>
 *   <li>双格式私钥支持（Base64和Base58）</li>
 *   <li>策略类内置完整转账实现</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Slf4j
@Component
public class SolanaTransferStrategy extends AbstractTransferStrategy {

    // ==================== 依赖注入 ====================

    private final SolanaHelper solanaHelper; // 用于基础转账API调用
    private final SolanaConfigFacade solanaFacade;

    /**
     * 监控指标收集器
     */
    @Autowired(required = false)
    private SolanaTransferMetrics transferMetrics;

    /**
     * 告警服务（复用TRON的告警服务）
     */
    @Autowired(required = false)
    private TronTransferAlertService alertService;

    /**
     * 构造函数
     * 注入依赖并设置事件发布器
     */
    public SolanaTransferStrategy(SolanaHelper solanaHelper,
                                  SolanaConfigFacade solanaFacade,
                                  ApplicationEventPublisher eventPublisher) {
        this.solanaHelper = solanaHelper;
        this.solanaFacade = solanaFacade;
        setEventPublisher(eventPublisher);
    }

    @Override
    public String getChainName() {
        return "SOLANA";
    }

    @Override
    public boolean supports(String chainName) {
        return "SOLANA".equalsIgnoreCase(chainName) || "SOL".equalsIgnoreCase(chainName);
    }

    // ==================== 抽象方法实现 ====================

    @Override
    protected void executeTransferInternal(TransferRequest request, BlockchainTransferResult result) {
        long startTime = System.currentTimeMillis();
        boolean isNativeToken = isNativeToken(request.getTokenSymbol());
        String transferType = isNativeToken ? "SOL" : "SPL";
        final String[] txHashHolder = {null}; // 使用数组来解决lambda变量引用问题

        try {
            // 1. 记录转账开始
            recordTransferStart(transferType);

            // 2. 使用统一异常转换器，简化异常处理
            solanaHelper.executeWithSolanaExceptionHandling(() -> {
                // 3. 统一手续费处理（增强错误处理）
                FeeProvisionResult feeResult = handleFeeProvisionWithRetry(request);

                // 4. 执行具体转账逻辑（增强错误处理）
                String hash = executeTransferWithRetry(request, isNativeToken);
                txHashHolder[0] = hash; // 保存txHash

                // 5. 异步转账确认（不阻塞转账流程）
                TransactionConfirmationResult confirmationResult = waitForTransactionConfirmationAsyncWithRecord(
                    hash, transferType + "转账", request, null);

                // 5.1. 转账记录更新由UnifiedTransferService统一管理，策略层不再处理

                // 6. 设置转账结果
                result.setSuccess(true);
                result.setTxHash(hash);
                result.setFeeProvided(feeResult.isProvided());
                result.setFeeAmount(feeResult.getAmount());
                result.setFeeTokenSymbol(feeResult.getTokenSymbol());
                result.setWaitedForConfirmation(confirmationResult.isConfirmed());
                result.setConfirmations(confirmationResult.getActualConfirmations());
                result.setConfirmationResult(confirmationResult);
                result.setConfirmationTime(confirmationResult.getEndTime());

                // 7. 记录转账成功
                long transferTime = System.currentTimeMillis() - startTime;
                long confirmationTime = confirmationResult.getConfirmationTimeMs();
                recordTransferSuccess(transferTime, confirmationTime, feeResult.getAmount(), feeResult.isProvided());

                // 8. 如果确认失败且配置要求确认失败导致转账失败，则抛出异常
                if (!confirmationResult.isConfirmed() && getConfirmationConfig(request).isConfirmationFailureCausesTransferFailure()) {
                    throw new BlockchainTransferException("Solana转账确认失败: " + confirmationResult.getErrorMessage(),
                        "SOLANA", "CONFIRMATION_FAILED");
                }

                log.info("Solana转账完成: txHash={}, type={}, time={}ms, feeProvided={}, confirmed={}",
                    hash, transferType, transferTime, feeResult.isProvided(), confirmationResult.isConfirmed());

                return null;
            });

        } catch (Exception e) {
            // 9. 增强异常处理
            long transferTime = System.currentTimeMillis() - startTime;
            handleTransferException(e, txHashHolder[0], transferTime, request);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    @Override
    protected String deriveFromAddress(String privateKey) {
        return getOwnerAddressFromPrivateKey(privateKey);
    }

    @Override
    protected FeeEstimate estimateTransferFee(TransferRequest request, boolean isNativeToken) {
        // 统一使用estimateSolanaFee()方法，因为SOL和SPL代币转账费用相同
        SolanaFeeEstimate solanaEstimate = estimateSolanaFee();

        return new org.dromara.wallet.wallet.transfer.dto.SolanaFeeEstimate(
            solanaEstimate.lamportsNeeded(),
            0L, // 账户租金，对于转账通常为0
            solanaEstimate.solNeeded()
        );
    }

    @Override
    protected boolean needsFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate) {
        return shouldUseFeeWallet(userAddress, 0L, feeEstimate.getNativeTokenNeeded());
    }

    @Override
    protected FeeProvisionResult provideFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate) {
        boolean success = provideSolanaFee(userAddress, feeEstimate.getNativeTokenNeeded());

        if (success) {
            return FeeProvisionResult.feeWalletSuccess(
                feeEstimate.getNativeTokenNeeded(),
                feeEstimate.getNativeTokenSymbol()
            );
        } else {
            return FeeProvisionResult.failure("手续费钱包提供SOL失败");
        }
    }

    @Override
    protected void validateTransferRequest(TransferRequest request) {
        // 调用父类基础验证
        super.validateTransferRequest(request);

        // Solana特定验证
        validateSolanaRequest(request);
    }

    @Override
    protected String getNativeTokenSymbol() {
        return "SOL";
    }

    // ==================== 核心转账实现 ====================

    /**
     * 执行SOL原生代币转账
     */
    private String executeNativeTransfer(TransferRequest request) {
        String fromAddress = deriveFromAddress(request.getPrivateKey());
        String toAddress = request.getToAddress();
        BigDecimal amount = request.getAmountAsBigDecimal();

        long lamports = amount.multiply(BigDecimal.valueOf(1_000_000_000L)).longValue();

        log.info("Solana原生代币转账开始: from={}, to={}, amount={} SOL ({} lamports)",
            fromAddress, toAddress, amount, lamports);

        String txHash = solanaHelper.transferNative(request.getPrivateKey(), toAddress, lamports);

        log.info("Solana原生代币转账成功: txHash={}", txHash);
        return txHash;
    }

    /**
     * 执行SPL代币转账
     */
    private String executeTokenTransfer(TransferRequest request) {
        String fromAddress = deriveFromAddress(request.getPrivateKey());
        String toAddress = request.getToAddress();
        BigDecimal amount = request.getAmountAsBigDecimal();
        String tokenSymbol = request.getTokenSymbol();

        String contractAddress = solanaFacade.getContractAddress(tokenSymbol);
        if (contractAddress == null || contractAddress.trim().isEmpty()) {
            throw new RuntimeException("Solana链不支持代币: " + tokenSymbol);
        }

        int decimals = solanaFacade.getContractDecimals(tokenSymbol);
        long rawAmount = amount.multiply(BigDecimal.TEN.pow(decimals)).longValue();

        log.info("Solana代币转账开始: from={}, to={}, token={}, amount={} {} (raw: {})",
            fromAddress, toAddress, tokenSymbol, amount, tokenSymbol, rawAmount);

        String txHash = solanaHelper.transferToken(request.getPrivateKey(), toAddress, rawAmount, contractAddress);

        log.info("Solana代币转账成功: txHash={}", txHash);
        return txHash;
    }

    // ==================== 基础转账方法（已迁移到SolanaHelper） ====================



    @Override
    public int getPriority() {
        return 30; // Solana策略优先级较低
    }

    @Override
    public boolean isAvailable() {
        try {
            // 检查SolanaHelper是否可用
            return solanaHelper != null;
        } catch (Exception e) {
            log.warn("Solana转账策略不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证Solana转账请求参数
     */
    private void validateSolanaRequest(TransferRequest request) {
        // 基础验证
        request.validate();

        // Solana特定验证
        String tokenSymbol = request.getTokenSymbol().toUpperCase();

        // 验证代币符号
        if (tokenSymbol.length() < 2 || tokenSymbol.length() > 10) {
            throw new IllegalArgumentException("Solana代币符号长度应在2-10个字符之间");
        }

        // 验证地址格式（Solana地址是Base58编码，长度通常在32-44个字符）
        String toAddress = request.getToAddress();
        if (toAddress.length() < 32 || toAddress.length() > 44) {
            throw new IllegalArgumentException("Solana地址格式不正确，长度应在32-44个字符之间");
        }

        // 验证地址字符集（Base58不包含0、O、I、l）
        if (toAddress.matches(".*[0OIl].*")) {
            throw new IllegalArgumentException("Solana地址格式不正确，不能包含字符0、O、I、l");
        }

        // 验证金额范围
        BigDecimal amount = request.getAmountAsBigDecimal();
        if (amount.compareTo(new BigDecimal("0.000000001")) < 0) {
            throw new IllegalArgumentException("Solana转账金额过小，最小值为0.000000001");
        }

        // 验证私钥格式（支持Base64和Base58两种格式）
        String privateKey = request.getPrivateKey();
        if (!isValidSolanaPrivateKey(privateKey)) {
            throw new IllegalArgumentException("Solana私钥格式不正确，应为Base64或Base58格式");
        }

        log.debug("Solana转账请求验证通过: token={}, amount={}, to={}",
            tokenSymbol, request.getAmount(), toAddress);
    }

    /**
     * 验证Solana私钥格式
     */
    private boolean isValidSolanaPrivateKey(String privateKey) {
        if (privateKey == null || privateKey.trim().isEmpty()) {
            return false;
        }

        // Base64格式检查（通常88个字符，以=结尾）
        if (privateKey.length() == 88 && privateKey.endsWith("=")) {
            try {
                java.util.Base64.getDecoder().decode(privateKey);
                return true;
            } catch (Exception e) {
                // 不是有效的Base64
            }
        }

        // Base58格式检查（通常64个字符）
        if (privateKey.length() >= 32 && privateKey.length() <= 88) {
            // 简单的Base58字符集检查
            return privateKey.matches("[1-9A-HJ-NP-Za-km-z]+");
        }

        return false;
    }

    // ==================== 辅助方法 ====================

    /**
     * 从私钥推导发送方地址 - 委托给SolanaHelper
     */
    private String getOwnerAddressFromPrivateKey(String privateKey) {
        return solanaHelper.getOwnerAddressFromPrivateKey(privateKey);
    }

    // ==================== 交易确认机制 ====================

    @Override
    protected TransactionConfirmationConfig getConfirmationConfig(TransferRequest request) {
        // Solana链确认配置：30秒超时，2秒间隔，基于400ms区块时间但考虑网络延迟
        return TransactionConfirmationConfig.solanaDefault();
    }

    protected TransactionConfirmationResult confirmTransactionInternal(String txHash, TransactionConfirmationConfig config) {
        try {
            log.debug("Solana: 开始真实交易确认 | txHash: {}, config: {}", txHash, config.getDescription());

            // 调用SolanaHelper的真实确认逻辑
            TransactionConfirmationResult result = solanaHelper.confirmTransaction(txHash, config);

            if (result.isSuccess()) {
                log.info("Solana: 交易确认成功 | txHash: {}, confirmations: {}",
                    txHash, result.getActualConfirmations());
            } else {
                log.warn("Solana: 交易确认失败 | txHash: {}, status: {}, error: {}",
                    txHash, result.getStatus(), result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("Solana: 交易确认异常 | txHash: {}, error: {}", txHash, e.getMessage());
            return TransactionConfirmationResult.failure(txHash,
                org.dromara.wallet.wallet.transfer.enums.TransactionStatus.UNKNOWN,
                "确认过程异常: " + e.getMessage());
        }
    }

    // ==================== 内部数据类已移至SolanaHelper ====================

    @Override
    public String getDescription() {
        return "Solana区块链转账策略，支持SOL和SPL代币，支持双格式私钥";
    }

    // ==================== 增强错误处理方法 ====================

    /**
     * 带重试的手续费处理
     */
    private FeeProvisionResult handleFeeProvisionWithRetry(TransferRequest request) {
        int maxRetries = 2;
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return handleFeeProvision(request);
            } catch (Exception e) {
                lastException = e;
                log.warn("Solana手续费处理失败，第{}次尝试: {}", attempt, e.getMessage());

                if (attempt < maxRetries) {
                    // 等待后重试
                    try {
                        Thread.sleep(1000L * attempt); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        // 所有重试都失败，抛出Solana特定异常
        throw SolanaTransferException.fromGenericException(lastException, null);
    }

    /**
     * 带重试的转账执行
     */
    private String executeTransferWithRetry(TransferRequest request, boolean isNativeToken) {
        int maxRetries = getMaxRetriesForTransfer();
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (isNativeToken) {
                    return executeNativeTransfer(request);
                } else {
                    return executeTokenTransfer(request);
                }
            } catch (Exception e) {
                lastException = e;
                SolanaTransferException solanaException = convertToSolanaException(e, null);

                log.warn("Solana转账执行失败，第{}次尝试: type={}, error={}",
                    attempt, solanaException.getErrorType().getDescription(), e.getMessage());

                // 检查是否可重试
                if (!solanaException.isRetryable() || attempt >= maxRetries) {
                    break;
                }

                // 等待后重试
                try {
                    Thread.sleep(2000L * attempt); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 所有重试都失败，抛出Solana特定异常
        throw convertToSolanaException(lastException, null);
    }

    /**
     * 转账异常处理
     */
    private void handleTransferException(Exception e, String txHash, long transferTime, TransferRequest request) {
        // 转换为Solana特定异常
        SolanaTransferException solanaException = convertToSolanaException(e, txHash);

        // 记录异常指标
        recordTransferFailure(solanaException.getErrorType().name(), transferTime);

        // 发送告警
        sendExceptionAlert(solanaException, txHash, buildExceptionContext(request));

        log.error("Solana转账异常处理: type={}, txHash={}, time={}ms, retryable={}, advice={}",
            solanaException.getErrorType().getDescription(), txHash, transferTime,
            solanaException.isRetryable(), solanaException.getRecoveryAdvice());
    }

    /**
     * 转换为Solana特定异常
     */
    private SolanaTransferException convertToSolanaException(Exception e, String txHash) {
        if (e instanceof SolanaTransferException) {
            return (SolanaTransferException) e;
        }
        return SolanaTransferException.fromGenericException(e, txHash);
    }

    /**
     * 构建异常上下文信息
     */
    private String buildExceptionContext(TransferRequest request) {
        return String.format("Chain=SOLANA, Token=%s, Amount=%s, To=%s",
            request.getTokenSymbol(), request.getAmount(), request.getToAddress());
    }

    /**
     * 获取转账最大重试次数
     */
    private int getMaxRetriesForTransfer() {
        return 2; // 默认重试2次
    }

    // ==================== 监控指标记录方法 ====================

    /**
     * 记录转账开始
     */
    private void recordTransferStart(String transferType) {
        if (transferMetrics != null) {
            transferMetrics.recordTransferStart(transferType);
        }
    }

    /**
     * 记录转账成功
     */
    private void recordTransferSuccess(long transferTime, long confirmationTime,
                                       BigDecimal feeAmount, boolean feeProvided) {
        if (transferMetrics != null) {
            transferMetrics.recordTransferSuccess(transferTime, confirmationTime, feeAmount, feeProvided);
        }
    }

    /**
     * 记录转账失败
     */
    private void recordTransferFailure(String exceptionType, long transferTime) {
        if (transferMetrics != null) {
            transferMetrics.recordTransferFailure(exceptionType, transferTime);
        }
    }

    /**
     * 发送异常告警
     */
    private void sendExceptionAlert(SolanaTransferException exception, String txHash, String context) {
        if (alertService != null && isAlertingEnabled()) {
            // 将Solana异常转换为通用告警格式
            alertService.sendCustomAlert(
                exception.getAlertLevel(),
                "Solana转账异常告警",
                exception.toString(),
                context
            );
        }
    }

    /**
     * 检查是否启用监控
     */
    private boolean isMonitoringEnabled() {
        return transferMetrics != null;
    }

    /**
     * 检查是否启用告警
     */
    private boolean isAlertingEnabled() {
        return alertService != null && isMonitoringEnabled();
    }

    // ==================== 手续费补给逻辑（从SolanaHelper迁移） ====================

    /**
     * 预估Solana转账手续费（统一版本）
     */
    private SolanaFeeEstimate estimateSolanaFee() {
        // Solana网络中SOL和SPL代币转账的基础费用都是5000 lamports
        long baseFee = 5000L; // 0.000005 SOL

        // 统一添加20%安全边际，避免网络拥堵时费用不足
        long safeFee = baseFee + (baseFee * 20 / 100);

        BigDecimal solNeeded = new BigDecimal(safeFee).divide(BigDecimal.valueOf(1_000_000_000L), 9, java.math.RoundingMode.UP);

        return new SolanaFeeEstimate(safeFee, solNeeded);
    }

    /**
     * 判断是否应该使用手续费钱包
     */
    private boolean shouldUseFeeWallet(String fromAddress, long transferAmount, BigDecimal feeNeeded) {
        try {
            java.math.BigInteger userSolBalance = solanaHelper.getBalance(fromAddress);
            BigDecimal userSolBalanceDecimal = new BigDecimal(userSolBalance)
                .divide(BigDecimal.valueOf(1_000_000_000L), 9, java.math.RoundingMode.DOWN);

            // 计算总需要的SOL（转账金额 + 手续费）
            BigDecimal transferAmountDecimal = new BigDecimal(transferAmount)
                .divide(BigDecimal.valueOf(1_000_000_000L), 9, java.math.RoundingMode.DOWN);
            BigDecimal totalNeeded = transferAmountDecimal.add(feeNeeded);

            boolean insufficient = userSolBalanceDecimal.compareTo(totalNeeded) < 0;
            if (insufficient) {
                log.info("Solana余额不足: address={}, current={}, needed={}, 启用手续费钱包",
                    fromAddress, userSolBalanceDecimal, totalNeeded);
            }
            return insufficient;
        } catch (Exception e) {
            log.error("检查用户SOL余额失败: {}", e.getMessage());
            return true; // 出错时使用手续费钱包
        }
    }

    /**
     * 为指定地址提供Solana手续费（公共入口）
     */
    private boolean provideSolanaFee(String userAddress, BigDecimal solNeeded) {
        return provideSolanaFeeWithRetry(userAddress, solNeeded);
    }

    /**
     * 为指定地址提供Solana手续费（带验证和重试）
     */
    private boolean provideSolanaFeeWithRetry(String userAddress, BigDecimal solNeeded) {
        int maxRetries = 3;
        int baseDelay = 2000; // 2秒基础延迟
        long startTime = System.currentTimeMillis();

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            log.info("尝试为地址 {} 提供Solana手续费，第 {}/{} 次，金额: {} SOL",
                userAddress, attempt, maxRetries, solNeeded);

            try {
                boolean success = provideSolanaFeeWithVerification(userAddress, solNeeded);
                if (success) {
                    long duration = System.currentTimeMillis() - startTime;
                    if (attempt > 1) {
                        log.info("Solana手续费提供在第 {} 次尝试后成功，总耗时: {}ms", attempt, duration);
                    }
                    return true;
                }

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxRetries) {
                    int delay = baseDelay * attempt; // 递增延迟：2秒、4秒、6秒
                    log.warn("第 {} 次提供Solana手续费失败，{}秒后重试", attempt, delay / 1000);
                    waitSafely(delay);
                }

            } catch (Exception e) {
                log.error("第 {} 次提供Solana手续费异常: {}", attempt, e.getMessage());

                // 判断是否应该重试
                if (shouldRetrySolanaOperation(e) && attempt < maxRetries) {
                    int delay = baseDelay * attempt;
                    log.info("第 {} 次提供Solana手续费异常，{}秒后重试: {}", attempt, delay / 1000, e.getMessage());
                    waitSafely(delay);
                } else {
                    log.error("第 {} 次提供Solana手续费异常，不可重试或已达最大重试次数: {}", attempt, e.getMessage());
                    break; // 不可重试的错误，直接退出
                }
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        log.error("Solana手续费提供在 {} 次尝试后仍然失败，总耗时: {}ms", maxRetries, duration);
        return false;
    }

    /**
     * 为指定地址提供Solana手续费（简化版本）
     */
    private boolean provideSolanaFeeWithVerification(String userAddress, BigDecimal solNeeded) {
        log.info("Solana手续费提供: address={}, 需要={} SOL", userAddress, solNeeded);

        try {
            // 检查手续费钱包是否启用
            if (!isFeeWalletEnabled()) {
                log.warn("Solana手续费钱包未启用");
                return false;
            }

            String feeWalletAddress = getFeeWalletAddress();
            String feeWalletPrivateKey = getFeeWalletPrivateKey();
            if (feeWalletAddress == null || feeWalletPrivateKey == null) {
                log.warn("Solana手续费钱包配置未找到");
                return false;
            }

            // 1. 获取用户当前SOL余额
            java.math.BigInteger userCurrentBalanceLamports = solanaHelper.getBalance(userAddress);
            BigDecimal userCurrentBalance = new BigDecimal(userCurrentBalanceLamports)
                .divide(BigDecimal.valueOf(1_000_000_000L), 9, java.math.RoundingMode.DOWN);

            // 2. 计算实际需要补给的差额
            BigDecimal actualNeeded = solNeeded.subtract(userCurrentBalance);
            if (actualNeeded.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("用户SOL余额已足够: {} >= {}，无需补给", userCurrentBalance, solNeeded);
                return true; // 用户余额已足够，无需补给
            }

            // 3. 检查手续费钱包余额是否足够
            java.math.BigInteger feeWalletBalance = solanaHelper.getBalance(feeWalletAddress);
            BigDecimal feeWalletBalanceDecimal = new BigDecimal(feeWalletBalance)
                .divide(BigDecimal.valueOf(1_000_000_000L), 9, java.math.RoundingMode.DOWN);

            if (feeWalletBalanceDecimal.compareTo(actualNeeded) < 0) {
                log.warn("手续费钱包SOL余额不足: {} < {}", feeWalletBalanceDecimal, actualNeeded);
                return false;
            }

            // 4. 从手续费钱包转账差额到用户地址
            long lamportsToTransfer = actualNeeded.multiply(BigDecimal.valueOf(1_000_000_000L)).longValue();
            String txHash = solanaHelper.transferNative(feeWalletPrivateKey, userAddress, lamportsToTransfer);
            log.info("Solana手续费补给已提交: 差额={} SOL, txHash={}", actualNeeded, txHash);

            // 5. 验证转账状态（简化验证，不等待确认）
            // 由于是手续费补给，我们相信交易会成功，不进行复杂的确认等待
            log.info("成功为地址 {} 补给 {} SOL 手续费", userAddress, actualNeeded);
            return true;

        } catch (Exception e) {
            log.error("Solana手续费提供异常: address={}, amount={}, error={}", userAddress, solNeeded, e.getMessage());
            return false;
        }
    }

    // ==================== 手续费钱包配置方法 ====================

    /**
     * 检查手续费钱包是否启用
     */
    private boolean isFeeWalletEnabled() {
        return solanaFacade.isFeeWalletEnabled();
    }

    /**
     * 获取手续费钱包地址
     */
    private String getFeeWalletAddress() {
        return solanaFacade.getFeeWalletAddress();
    }

    /**
     * 获取手续费钱包私钥
     */
    private String getFeeWalletPrivateKey() {
        return solanaFacade.getFeeWalletPrivateKey();
    }

    /**
     * 检查手续费钱包余额
     */
    private BigDecimal checkFeeWalletBalance() {
        log.debug("检查Solana手续费钱包余额");

        if (!isFeeWalletEnabled()) {
            log.warn("Solana手续费钱包未启用");
            return BigDecimal.ZERO;
        }

        try {
            String feeWalletAddress = getFeeWalletAddress();
            java.math.BigInteger balance = solanaHelper.getBalance(feeWalletAddress);
            return new BigDecimal(balance).divide(BigDecimal.valueOf(1_000_000_000L), 9, java.math.RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.error("检查Solana手续费钱包余额失败", e);
            return BigDecimal.ZERO;
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 安全等待指定时间
     */
    private void waitSafely(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("等待被中断");
        }
    }

    /**
     * 判断是否应该重试Solana操作
     */
    private boolean shouldRetrySolanaOperation(Exception e) {
        String message = e.getMessage();
        if (message == null) return false;

        String lowerMessage = message.toLowerCase();
        // 网络相关错误可以重试
        return lowerMessage.contains("timeout") ||
            lowerMessage.contains("connection") ||
            lowerMessage.contains("network") ||
            lowerMessage.contains("rpc") ||
            lowerMessage.contains("temporary");
    }

    // ==================== 内部数据类 ====================

    /**
     * Solana手续费预估结果
     */
    private record SolanaFeeEstimate(long lamportsNeeded, BigDecimal solNeeded) {
    }
}
