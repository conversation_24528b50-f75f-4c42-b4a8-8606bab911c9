package org.dromara.wallet.wallet.monitor.evm.event;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.scanning.chain.model.TransactionModel;
import org.dromara.common.scanning.monitor.EthMonitorEvent;
import org.dromara.common.scanning.monitor.filter.EthMonitorFilter;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.dromara.wallet.service.IMetaBep20CstaddressinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.web3j.protocol.core.methods.response.Log;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * EVM地址预过滤事件
 * 负责检查交易是否涉及监控地址，实现早期过滤以提升性能
 *
 * <p>功能特性：</p>
 * <ul>
 *   <li>地址预过滤：检查Log中的地址是否涉及监控的钱包地址或合约地址</li>
 *   <li>性能优化：早期过滤减少后续不必要的处理</li>
 *   <li>支持ERC20和ETH原生转账的地址检查</li>
 *   <li>支持多链：BSC、ARB、BASE等EVM兼容链</li>
 *   <li>根据TransactionModel的chainType动态选择对应的ConfigFacade</li>
 *   <li>地址监控逻辑：集成了从EvmTransactionManager移动过来的地址监控功能</li>
 * </ul>
 *
 * <p>重构说明：</p>
 * <ul>
 *   <li>移除对EvmTransactionManager的依赖，直接依赖IMetaBep20CstaddressinfoService</li>
 *   <li>将getMonitoredAddresses和isMonitoredTransaction方法从EvmTransactionManager移动到此处</li>
 *   <li>实现自包含的地址监控逻辑，消除循环依赖</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Component
public class EvmAddressFilterEvent implements EthMonitorEvent {

    private final IMetaBep20CstaddressinfoService evmAddressService;

    /**
     * 链配置门面映射表
     * key: 链名称 (BSC, ARB, BASE)
     * value: 对应的EvmConfigFacade实现
     */
    private final Map<String, EvmConfigFacade> configFacadeMap;

    /**
     * 地址过滤结果存储键名
     */
    public static final String ADDRESS_FILTER_RESULT_KEY = "evm.addressFilterPassed";

    /**
     * 构造函数，自动注入所有EvmConfigFacade实现并构建映射表
     * 重构说明：移除对EvmTransactionManager的依赖，直接依赖地址服务
     */
    @Autowired
    public EvmAddressFilterEvent(IMetaBep20CstaddressinfoService evmAddressService,
                                List<EvmConfigFacade> configFacades) {
        this.evmAddressService = evmAddressService;
        this.configFacadeMap = configFacades.stream()
            .collect(Collectors.toMap(
                EvmConfigFacade::getChainName,
                Function.identity()
            ));

        log.info("EvmAddressFilterEvent初始化完成，支持的链: {}", configFacadeMap.keySet());
    }



    @Override
    public EthMonitorFilter ethMonitorFilter() {
        // 不在这里进行过滤，接受所有交易进行地址检查
        return EthMonitorFilter.builder()
            .setMinValue(BigInteger.ZERO);
    }

    @Override
    public List<String> getMonitorContractAddresses() {
        // 合并所有链的监控合约地址，用于RPC层面的过滤优化（向后兼容）
        List<String> allContractAddresses = configFacadeMap.values().stream()
            .filter(EvmConfigFacade::isEnabled)
            .flatMap(facade -> facade.getEnabledContractAddresses().stream())
            .distinct()
            .collect(Collectors.toList());

        if (!allContractAddresses.isEmpty()) {
            log.debug("EVM链提供{}个监控合约地址用于RPC过滤", allContractAddresses.size());
        }

        return allContractAddresses;
    }

    @Override
    public List<String> getMonitorContractAddresses(String chainType) {
        // 根据链类型过滤合约地址，解决跨链地址混合问题
        if (chainType == null) {
            log.warn("链类型为空，返回所有链的合约地址");
            return getMonitorContractAddresses();
        }

        EvmConfigFacade configFacade = configFacadeMap.get(chainType);
        if (configFacade == null) {
            log.warn("未找到链类型 {} 的配置门面，返回空列表", chainType);
            return List.of();
        }

        if (!configFacade.isEnabled()) {
            log.debug("链类型 {} 未启用，返回空列表", chainType);
            return List.of();
        }

        List<String> chainContractAddresses = configFacade.getEnabledContractAddresses();

        if (!chainContractAddresses.isEmpty()) {
            log.debug("{}链提供{}个监控合约地址用于RPC过滤: {}",
                chainType, chainContractAddresses.size(), chainContractAddresses);
        } else {
            log.debug("{}链没有启用的监控合约地址", chainType);
        }

        return chainContractAddresses;
    }

    @Override
    public void call(TransactionModel transactionModel) {
        try {
            // 获取链类型标识
            String chainType = transactionModel.getChainType();
            if (chainType == null) {
                log.debug("交易模型缺少链类型标识，跳过地址过滤");
                return;
            }

            // 获取对应链的配置门面
            EvmConfigFacade configFacade = getConfigFacade(chainType);
            if (configFacade == null) {
                log.debug("{}链没有对应的配置门面，跳过地址过滤", chainType);
                return;
            }

            // 检查是否为EVM交易模型
            if (transactionModel.getEthTransactionModel() == null) {
                log.debug("{}链跳过非EVM交易模型", chainType);
                return;
            }

            // 获取Log对象
            Log logData = transactionModel.getEthTransactionModel().getLog();
            if (logData == null) {
                log.debug("{}链交易没有Log数据，跳过地址过滤", chainType);
                return;
            }

            String txHash = logData.getTransactionHash();
            if (txHash == null) {
                log.debug("{}链交易哈希为空，跳过地址过滤", chainType);
                return;
            }

            // 执行地址过滤检查
            boolean shouldProcess = shouldProcessTransaction(logData, configFacade);

            // 存储地址过滤结果到处理上下文
            storeAddressFilterResult(transactionModel, shouldProcess);

            if (shouldProcess) {
                log.debug("{}链交易{}通过地址过滤检查", chainType, txHash);
            } else {
                log.debug("{}链交易{}未通过地址过滤检查，跳过后续处理", chainType, txHash);
            }

        } catch (Exception e) {
            String chainType = transactionModel.getChainType();
            log.error("{}链地址过滤失败: {}", chainType != null ? chainType : "UNKNOWN", e.getMessage());
            // 异常时存储false，表示过滤失败
            storeAddressFilterResult(transactionModel, false);
        }
    }

    /**
     * 根据链类型获取对应的配置门面
     */
    private EvmConfigFacade getConfigFacade(String chainType) {
        return configFacadeMap.get(chainType);
    }

    /**
     * 存储地址过滤结果到处理上下文
     */
    private void storeAddressFilterResult(TransactionModel transactionModel, boolean passed) {
        transactionModel.putContextValue(ADDRESS_FILTER_RESULT_KEY, passed);

        String chainType = transactionModel.getChainType();
        String txHash = "unknown";
        if (transactionModel.getEthTransactionModel() != null
            && transactionModel.getEthTransactionModel().getLog() != null) {
            txHash = transactionModel.getEthTransactionModel().getLog().getTransactionHash();
        }

        log.trace("{}链交易{}地址过滤结果已存储: {}",
            chainType != null ? chainType : "UNKNOWN", txHash, passed);
    }

    /**
     * 静态方法：获取地址过滤结果
     * 供后续的MonitorEvent使用
     */
    public static Boolean getAddressFilterResult(TransactionModel transactionModel) {
        if (transactionModel == null) {
            return null;
        }
        return transactionModel.getContextValue(ADDRESS_FILTER_RESULT_KEY, Boolean.class);
    }

    /**
     * 检查交易是否应该被处理
     * 判断Log中的地址是否涉及监控地址
     */
    private boolean shouldProcessTransaction(Log logData, EvmConfigFacade configFacade) {
        // 获取监控地址集合
        Set<String> monitoredWalletAddresses = getMonitoredWalletAddresses();
        Set<String> monitoredContractAddresses = getMonitoredContractAddresses(configFacade);

        if (monitoredWalletAddresses.isEmpty() && monitoredContractAddresses.isEmpty()) {
            log.debug("{}链没有配置监控地址，跳过所有交易", configFacade.getChainName());
            return false;
        }

        // 检查合约地址（Log.address）
        String contractAddress = logData.getAddress();
        boolean contractMatches = containsMonitoredAddress(contractAddress, monitoredContractAddresses);

        // 检查 Transfer事件中的from和to地址
        boolean walletMatches = false;
        if (isTransferEvent(logData)) {
            walletMatches = checkTransferEventAddresses(logData, monitoredWalletAddresses);
        }

        boolean shouldProcess = contractMatches && walletMatches;

        if (log.isDebugEnabled()) {
            log.debug("{}链交易{}地址检查结果: 合约地址匹配={}, 钱包地址匹配={}, 最终结果={}",
                configFacade.getChainName(), logData.getTransactionHash(),
                contractMatches, walletMatches, shouldProcess);
        }

        return shouldProcess;
    }

    /**
     * 检查是否为Transfer事件
     */
    private boolean isTransferEvent(Log logData) {
        List<String> topics = logData.getTopics();
        if (topics == null || topics.isEmpty()) {
            return false;
        }

        // Transfer事件的topic签名
        String transferEventTopic = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";
        return transferEventTopic.equals(topics.get(0));
    }

    /**
     * 检查Transfer事件中的地址
     */
    private boolean checkTransferEventAddresses(Log logData, Set<String> monitoredWalletAddresses) {
        List<String> topics = logData.getTopics();
        if (topics.size() < 3) {
            return false;
        }

        try {
            // 从topics中提取from和to地址
            String fromAddress = extractAddressFromTopic(topics.get(1));
            String toAddress = extractAddressFromTopic(topics.get(2));

            // 检查from或to地址是否在监控列表中
            return containsMonitoredAddress(fromAddress, monitoredWalletAddresses) ||
                   containsMonitoredAddress(toAddress, monitoredWalletAddresses);

        } catch (Exception e) {
            log.warn("解析Transfer事件地址失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从topic中提取地址
     */
    private String extractAddressFromTopic(String topic) {
        if (topic == null || topic.length() < 66) {
            return null;
        }
        // topic格式：0x000000000000000000000000{20字节地址}
        // 提取后40个字符（20字节地址）并添加0x前缀
        return "0x" + topic.substring(26);
    }

    /**
     * 检查地址是否在监控集合中
     */
    private boolean containsMonitoredAddress(String address, Set<String> monitoredAddresses) {
        if (address == null || monitoredAddresses.isEmpty()) {
            return false;
        }
        return monitoredAddresses.contains(address.toLowerCase());
    }

    /**
     * 获取监控的合约地址集合
     */
    private Set<String> getMonitoredContractAddresses(EvmConfigFacade configFacade) {
        return configFacade.getEnabledContractAddresses().stream()
            .map(String::toLowerCase)
            .collect(Collectors.toSet());
    }

    /**
     * 获取监控的钱包地址集合
     * 重构说明：从EvmTransactionManager移动到此处，消除依赖
     */
    private Set<String> getMonitoredWalletAddresses() {
        return getMonitoredAddresses().stream()
            .map(String::toLowerCase)
            .collect(Collectors.toSet());
    }

    /**
     * 获取监控地址集合
     * 从EvmTransactionManager移动到此处的方法
     *
     * @return 监控地址集合
     */
    public Set<String> getMonitoredAddresses() {
        // 获取EVM监控地址集合，用于性能优化
        return TenantHelper.ignore(evmAddressService::queryAllAddressSet);
    }


}
