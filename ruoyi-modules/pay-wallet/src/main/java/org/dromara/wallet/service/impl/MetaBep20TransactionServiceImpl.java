package org.dromara.wallet.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.wallet.domain.bo.MetaBep20TransactionBo;
import org.dromara.wallet.domain.vo.MetaBep20TransactionVo;
import org.dromara.wallet.domain.MetaBep20Transaction;
import org.dromara.wallet.mapper.MetaBep20TransactionMapper;
import org.dromara.wallet.service.IMetaBep20TransactionService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * BSC区块高度交易明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MetaBep20TransactionServiceImpl implements IMetaBep20TransactionService {

    private final MetaBep20TransactionMapper baseMapper;

    /**
     * 查询BSC区块高度交易明细
     *
     * @param id 主键
     * @return BSC区块高度交易明细
     */
    @Override
    public MetaBep20TransactionVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 检查交易哈希是否已存在
     *
     * @param txid 交易哈希
     * @return 是否存在
     */
    @Override
    public Boolean isExist(String txid) {
        LambdaQueryWrapper<MetaBep20Transaction> wrapper = new LambdaQueryWrapper<MetaBep20Transaction>()
            .eq(MetaBep20Transaction::getTxid, txid);
        return baseMapper.exists(wrapper);
    }

    /**
     * 分页查询BSC区块高度交易明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return BSC区块高度交易明细分页列表
     */
    @Override
    public TableDataInfo<MetaBep20TransactionVo> queryPageList(MetaBep20TransactionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MetaBep20Transaction> lqw = buildQueryWrapper(bo);
        Page<MetaBep20TransactionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的BSC区块高度交易明细列表
     *
     * @param bo 查询条件
     * @return BSC区块高度交易明细列表
     */
    @Override
    public List<MetaBep20TransactionVo> queryList(MetaBep20TransactionBo bo) {
        LambdaQueryWrapper<MetaBep20Transaction> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MetaBep20Transaction> buildQueryWrapper(MetaBep20TransactionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MetaBep20Transaction> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MetaBep20Transaction::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getTxid()), MetaBep20Transaction::getTxid, bo.getTxid());
        lqw.eq(bo.getBlockheight() != null, MetaBep20Transaction::getBlockheight, bo.getBlockheight());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), MetaBep20Transaction::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getFromaddress()), MetaBep20Transaction::getFromaddress, bo.getFromaddress());
        lqw.eq(StringUtils.isNotBlank(bo.getContract()), MetaBep20Transaction::getContract, bo.getContract());
        lqw.eq(bo.getAmount() != null, MetaBep20Transaction::getAmount, bo.getAmount());
        lqw.eq(bo.getFee() != null, MetaBep20Transaction::getFee, bo.getFee());
        lqw.eq(bo.getTimestamp() != null, MetaBep20Transaction::getTimestamp, bo.getTimestamp());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), MetaBep20Transaction::getType, bo.getType());
        lqw.eq(bo.getIssync() != null, MetaBep20Transaction::getIssync, bo.getIssync());
        return lqw;
    }

    /**
     * 新增BSC区块高度交易明细
     *
     * @param bo BSC区块高度交易明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MetaBep20TransactionBo bo) {
        MetaBep20Transaction add = MapstructUtils.convert(bo, MetaBep20Transaction.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改BSC区块高度交易明细
     *
     * @param bo BSC区块高度交易明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MetaBep20TransactionBo bo) {
        MetaBep20Transaction update = MapstructUtils.convert(bo, MetaBep20Transaction.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MetaBep20Transaction entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除BSC区块高度交易明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
