package org.dromara.wallet.wallet.transfer.strategy.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.wallet.config.facade.ChainConfigFacadeManager;
import org.dromara.wallet.config.facade.EvmConfigFacade;
import org.dromara.wallet.wallet.exception.BlockchainTransferException;
import org.dromara.wallet.wallet.exception.WalletException;
import org.dromara.wallet.wallet.helper.EvmHelper;
import org.dromara.wallet.wallet.transfer.alert.TronTransferAlertService;
import org.dromara.wallet.wallet.transfer.config.TransactionConfirmationConfig;
import org.dromara.wallet.wallet.transfer.dto.*;
import org.dromara.wallet.wallet.transfer.enums.TransactionStatus;
import org.dromara.wallet.wallet.transfer.exception.EvmTransferException;
import org.dromara.wallet.wallet.transfer.monitor.EvmTransferMetrics;
import org.dromara.wallet.wallet.transfer.monitor.EvmTransferMonitorReport;
import org.dromara.wallet.wallet.transfer.strategy.AbstractTransferStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.response.EthGasPrice;
import org.web3j.protocol.core.methods.response.EthGetTransactionCount;
import org.web3j.protocol.core.methods.response.EthSendTransaction;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * EVM转账策略实现 - 完整迁移版本
 *
 * <p>封装EVM兼容链的转账逻辑，支持以下特性：</p>
 * <ul>
 *   <li>支持BSC、ARB、BASE、ETH等EVM兼容链</li>
 *   <li>原生代币转账（BNB、ETH等）</li>
 *   <li>ERC20/BEP20合约代币转账</li>
 *   <li>Gas费用优化和手续费钱包支持</li>
 *   <li>交易确认和验证</li>
 *   <li>策略类内置完整转账实现</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/7/19
 */
@Slf4j
@Component
public class EvmTransferStrategy extends AbstractTransferStrategy {

    // ==================== 依赖注入 ====================

    private final EvmHelper evmHelper; // 保留用于基础API调用
    private final ChainConfigFacadeManager chainConfigFacadeManager;

    /**
     * 监控指标收集器
     */
    @Autowired(required = false)
    private EvmTransferMetrics transferMetrics;

    /**
     * 告警服务（复用TRON的告警服务）
     */
    @Autowired(required = false)
    private TronTransferAlertService alertService;

    /**
     * 构造函数
     * 注入依赖并设置事件发布器
     */
    public EvmTransferStrategy(EvmHelper evmHelper,
                               ChainConfigFacadeManager chainConfigFacadeManager,
                               ApplicationEventPublisher eventPublisher) {
        this.evmHelper = evmHelper;
        this.chainConfigFacadeManager = chainConfigFacadeManager;
        setEventPublisher(eventPublisher);
    }

    // ==================== 常量定义 ====================

    /**
     * 支持的EVM兼容链列表
     */
    private static final List<String> SUPPORTED_CHAINS = Arrays.asList(
        "BSC", "BNB", "BINANCE",
        "ARB", "ARBITRUM",
        "BASE",
        "ETH", "ETHEREUM"
    );

    /**
     * ERC20 Transfer事件签名: Transfer(address indexed from, address indexed to, uint256 value)
     * Keccak256哈希值: 0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef
     */
    private static final String TRANSFER_EVENT_TOPIC = "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef";

    /**
     * 默认Gas限制
     */
    private static final BigInteger DEFAULT_GAS_LIMIT = BigInteger.valueOf(21000);

    /**
     * 代币转账Gas限制
     */
    private static final BigInteger TOKEN_TRANSFER_GAS_LIMIT = BigInteger.valueOf(100000);

    // ==================== 策略接口实现 ====================

    @Override
    public String getChainName() {
        return "EVM"; // 通用EVM策略
    }

    @Override
    public boolean supports(String chainName) {
        return SUPPORTED_CHAINS.contains(chainName.toUpperCase());
    }

    // ==================== 抽象方法实现 ====================

    @Override
    protected void executeTransferInternal(TransferRequest request, BlockchainTransferResult result) {
        long startTime = System.currentTimeMillis();
        boolean isNativeToken = isNativeToken(request.getTokenSymbol());
        String transferType = isNativeToken ? "原生代币" : "ERC20";
        final String[] txHashHolder = {null}; // 使用数组来解决lambda变量引用问题

        try {
            // 1. 记录转账开始
            recordTransferStart(request.getChainName(), transferType);

            // 2. 使用统一异常转换器，简化异常处理
            evmHelper.executeWithEvmExceptionHandling(() -> {
                // 3. 统一手续费处理（增强错误处理）
                FeeProvisionResult feeResult = handleFeeProvisionWithRetry(request);

                // 4. 获取配置门面
                EvmConfigFacade facade = chainConfigFacadeManager.getEvmConfigFacade(request.getChainName());

                // 5. 执行具体转账逻辑（增强错误处理）
                String hash = executeTransferWithRetry(request, facade, isNativeToken);
                txHashHolder[0] = hash; // 保存txHash

                // 6. 异步转账确认（不阻塞转账流程）
                TransactionConfirmationResult confirmationResult = waitForTransactionConfirmationAsyncWithRecord(
                    hash, transferType + "转账", request, null);

                // 6.1. 转账记录更新由UnifiedTransferService统一管理，策略层不再处理

                // 7. 设置转账结果
                result.setSuccess(true);
                result.setTxHash(hash);
                result.setFeeProvided(feeResult.isProvided());
                result.setFeeAmount(feeResult.getAmount());
                result.setFeeTokenSymbol(feeResult.getTokenSymbol());
                result.setWaitedForConfirmation(confirmationResult.isConfirmed());
                result.setConfirmations(confirmationResult.getActualConfirmations());
                result.setConfirmationResult(confirmationResult);
                result.setConfirmationTime(confirmationResult.getEndTime());

                // 8. 记录转账成功
                long transferTime = System.currentTimeMillis() - startTime;
                long confirmationTime = confirmationResult.getConfirmationTimeMs();
                recordTransferSuccess(request.getChainName(), transferTime, confirmationTime,
                                    feeResult.getAmount(), feeResult.isProvided());

                // 9. 如果确认失败且配置要求确认失败导致转账失败，则抛出异常
                if (!confirmationResult.isConfirmed() && getConfirmationConfig(request).isConfirmationFailureCausesTransferFailure()) {
                    throw new BlockchainTransferException("EVM转账确认失败: " + confirmationResult.getErrorMessage(),
                        request.getChainName(), "CONFIRMATION_FAILED");
                }

                log.info("{}转账完成: txHash={}, type={}, time={}ms, feeProvided={}, confirmed={}",
                        request.getChainName(), hash, transferType, transferTime,
                        feeResult.isProvided(), confirmationResult.isConfirmed());

                return null;
            });

        } catch (Exception e) {
            // 10. 增强异常处理
            long transferTime = System.currentTimeMillis() - startTime;
            handleTransferException(e, request.getChainName(), txHashHolder[0], transferTime, request);
            throw e; // 重新抛出异常，让上层处理
        }
    }

    @Override
    protected String deriveFromAddress(String privateKey) {
        return evmHelper.getAddressFromPrivateKey(privateKey);
    }

    @Override
    protected FeeEstimate estimateTransferFee(TransferRequest request, boolean isNativeToken) {
        EvmConfigFacade facade = chainConfigFacadeManager.getEvmConfigFacade(request.getChainName());
        String fromAddress = deriveFromAddress(request.getPrivateKey());
        String toAddress = request.getToAddress();
        BigDecimal amount = request.getAmountAsBigDecimal();
        String tokenSymbol = request.getTokenSymbol();

        EvmHelper.EvmFeeEstimate evmEstimate;
        if (isNativeToken) {
            // 原生代币转账手续费估算 - 使用合约地址为null来表示原生代币
            evmEstimate = evmHelper.estimateTokenTransferFee(fromAddress, toAddress, amount, tokenSymbol, facade);
        } else {
            // 合约代币转账手续费估算
            evmEstimate = evmHelper.estimateTokenTransferFee(fromAddress, toAddress, amount, tokenSymbol, facade);
        }

        return EvmFeeEstimate.forNativeTransfer(
            evmEstimate.gasPrice(),
            evmEstimate.gasLimit(),
            evmEstimate.nativeTokenNeeded(),
            configGetNativeTokenSymbol(facade)
        );
    }

    @Override
    protected boolean needsFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate) {
        EvmConfigFacade facade = chainConfigFacadeManager.getEvmConfigFacade(request.getChainName());

        try {
            // 获取用户当前原生代币余额
            BigDecimal userBalance = evmHelper.balanceGetNativeForRead(userAddress, facade);

            // 如果用户余额不足以支付手续费，则需要使用手续费钱包
            boolean needFeeWallet = userBalance.compareTo(feeEstimate.getNativeTokenNeeded()) < 0;

            log.debug("{}手续费检查: 用户余额={}, 需要={}, 使用手续费钱包={}",
                facade.getChainName(), userBalance, feeEstimate.getNativeTokenNeeded(), needFeeWallet);

            return needFeeWallet;

        } catch (Exception e) {
            log.error("检查{}手续费钱包需求失败", facade.getChainName(), e);
            // 出错时保守处理，使用手续费钱包
            return true;
        }
    }

    @Override
    protected FeeProvisionResult provideFeeSupport(TransferRequest request, String userAddress, FeeEstimate feeEstimate) {
        EvmConfigFacade facade = chainConfigFacadeManager.getEvmConfigFacade(request.getChainName());
        BigDecimal nativeTokenNeeded = feeEstimate.getNativeTokenNeeded();
        String nativeSymbol = evmHelper.configGetNativeTokenSymbol(facade);

        log.info("EVM: 开始提供手续费 | chain: {} | address: {} | 预估{}: {}",
            facade.getChainName(), userAddress, nativeSymbol, nativeTokenNeeded);

        // 使用带重试的手续费提供逻辑
        boolean success = provideFeeWithRetry(userAddress, facade, nativeTokenNeeded);

        if (success) {
            log.info("EVM: 手续费提供成功 | chain: {} | address: {}", facade.getChainName(), userAddress);
            return FeeProvisionResult.feeWalletSuccess(nativeTokenNeeded, nativeSymbol);
        } else {
            log.error("EVM: 手续费提供失败 | chain: {} | address: {}", facade.getChainName(), userAddress);
            return FeeProvisionResult.failure("手续费钱包提供" + nativeSymbol + "失败");
        }
    }

    @Override
    protected void validateTransferRequest(TransferRequest request) {
        // 调用父类基础验证
        super.validateTransferRequest(request);

        // EVM特定验证
        validateEvmRequest(request);
    }

    @Override
    protected String getNativeTokenSymbol() {
        return "ETH"; // 默认ETH，具体链会在转账时动态获取
    }

    // ==================== 核心转账实现 ====================

    /**
     * 执行原生代币转账
     */
    private String executeNativeTransfer(TransferRequest request, EvmConfigFacade facade) {
        String fromAddress = deriveFromAddress(request.getPrivateKey());
        String txHash = transferNative(fromAddress, request.getPrivateKey(),
            request.getAmountAsBigDecimal(), request.getToAddress(), facade);

        log.info("{}原生代币转账成功: txHash={}", facade.getChainName(), txHash);
        return txHash;
    }

    /**
     * 执行合约代币转账
     */
    private String executeTokenTransfer(TransferRequest request, EvmConfigFacade facade) {
        String fromAddress = deriveFromAddress(request.getPrivateKey());
        String tokenSymbol = request.getTokenSymbol();
        BigDecimal amount = request.getAmountAsBigDecimal();

        log.info("{}代币转账开始: from={}, to={}, token={}, amount={}",
            facade.getChainName(), fromAddress, request.getToAddress(), tokenSymbol, amount);

        // 执行转账
        String txHash = transferToken(fromAddress, request.getPrivateKey(), amount,
            request.getToAddress(), tokenSymbol, facade);

        log.info("{}代币转账成功: txHash={}", facade.getChainName(), txHash);
        return txHash;
    }

    @Override
    public int getPriority() {
        return 20; // EVM策略优先级中等
    }

    // ==================== 基础转账方法 ====================

    /**
     * 原生代币转账 - 内部实现方法
     * 委托给EvmHelper的基础API方法
     */
    private String transferNative(String fromAddress, String privateKey, BigDecimal amount,
                                  String toAddress, EvmConfigFacade facade) {
        Web3j web3j = configGetWeb3j(facade);

        try {
            // 获取nonce
            EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(
                fromAddress, DefaultBlockParameterName.LATEST).send();
            BigInteger nonce = ethGetTransactionCount.getTransactionCount();

            // 获取gas价格
            EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
            BigInteger gasPrice = ethGasPrice.getGasPrice();

            // 应用gas价格上限（防止网络拥堵时价格过高）
            BigInteger maxGasPrice = BigInteger.valueOf(facade.getMaxGasPrice());
            if (gasPrice.compareTo(maxGasPrice) > 0) {
                gasPrice = maxGasPrice;
                log.debug("{}链gas价格超过上限，使用配置上限: {} wei", facade.getChainName(), maxGasPrice);
            }

            // 转换金额为Wei
            BigInteger value = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger();

            // 创建交易
            RawTransaction rawTransaction = RawTransaction.createEtherTransaction(
                nonce, gasPrice, DEFAULT_GAS_LIMIT, toAddress, value);

            // 签名交易
            Credentials credentials = Credentials.create(privateKey);
            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction,
                facade.getChainId(), credentials);
            String hexValue = Numeric.toHexString(signedMessage);

            // 发送交易
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).send();

            if (ethSendTransaction.hasError()) {
                throw new WalletException(facade.getChainName() + "原生代币转账失败: " +
                    ethSendTransaction.getError().getMessage());
            }

            String txHash = ethSendTransaction.getTransactionHash();
            log.info("{}原生代币转账成功: from={}, to={}, amount={}, txHash={}",
                facade.getChainName(), fromAddress, toAddress, amount, txHash);

            return txHash;

        } catch (Exception e) {
            log.error("{}原生代币转账异常: {}", facade.getChainName(), e.getMessage());
            throw new WalletException(facade.getChainName() + "原生代币转账失败: " + e.getMessage());
        }
    }

    /**
     * 合约代币转账 - 内部实现方法（使用代币符号）
     */
    private String transferToken(String fromAddress, String privateKey, BigDecimal amount,
                                 String toAddress, String tokenSymbol, EvmConfigFacade facade) {
        // 通过代币符号获取合约地址
        String contractAddress = facade.getContractAddress(tokenSymbol);
        if (contractAddress == null || contractAddress.trim().isEmpty()) {
            throw new WalletException(facade.getChainName() + "链不支持代币: " + tokenSymbol);
        }

        // 直接获取精度并调用主要方法
        int decimals = facade.getContractDecimals(tokenSymbol);
        return transferTokenByContract(fromAddress, privateKey, amount, toAddress,
            contractAddress, facade, decimals);
    }

    @Override
    public boolean isAvailable() {
        try {
            // 检查依赖是否可用
            return evmHelper != null && chainConfigFacadeManager != null;
        } catch (Exception e) {
            log.warn("EVM转账策略不可用: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 合约代币转账 - 内部实现方法（直接使用合约地址）
     */
    private String transferTokenByContract(String fromAddress, String privateKey, BigDecimal amount,
                                           String toAddress, String contractAddress, EvmConfigFacade facade,
                                           int decimals) {
        Web3j web3j = configGetWeb3j(facade);

        try {
            // 获取nonce
            EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(
                fromAddress, DefaultBlockParameterName.LATEST).send();
            BigInteger nonce = ethGetTransactionCount.getTransactionCount();

            // 获取gas价格
            EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
            BigInteger gasPrice = ethGasPrice.getGasPrice();

            // 应用gas价格上限（防止网络拥堵时价格过高）
            BigInteger maxGasPrice = BigInteger.valueOf(facade.getMaxGasPrice());
            if (gasPrice.compareTo(maxGasPrice) > 0) {
                gasPrice = maxGasPrice;
                log.debug("{}链gas价格超过上限，使用配置上限: {} wei", facade.getChainName(), maxGasPrice);
            }

            // 转换金额为最小单位
            BigInteger amountRaw = amount.multiply(BigDecimal.TEN.pow(decimals)).toBigInteger();

            // 构建transfer函数调用
            Function function = new Function("transfer",
                Arrays.asList(new Address(toAddress), new Uint256(amountRaw)),
                Collections.emptyList());
            String encodedFunction = FunctionEncoder.encode(function);

            // 估算Gas限制
            BigInteger gasLimit = estimateGasForToken(web3j, fromAddress, contractAddress,
                encodedFunction, facade);

            // 创建交易
            RawTransaction rawTransaction = RawTransaction.createTransaction(
                nonce, gasPrice, gasLimit, contractAddress, encodedFunction);

            // 签名交易
            Credentials credentials = Credentials.create(privateKey);
            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction,
                facade.getChainId(), credentials);
            String hexValue = Numeric.toHexString(signedMessage);

            // 发送交易
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).send();

            if (ethSendTransaction.hasError()) {
                throw new WalletException(facade.getChainName() + "代币转账失败: " +
                    ethSendTransaction.getError().getMessage());
            }

            String txHash = ethSendTransaction.getTransactionHash();
            log.info("{}代币转账成功: from={}, to={}, contract={}, amount={}, txHash={}",
                facade.getChainName(), fromAddress, toAddress, contractAddress, amount, txHash);

            return txHash;

        } catch (Exception e) {
            log.error("{}代币转账异常: {}", facade.getChainName(), e.getMessage());
            throw new WalletException(facade.getChainName() + "代币转账失败: " + e.getMessage());
        }
    }

    // ==================== 参数验证 ====================

    /**
     * 验证EVM转账请求参数
     */
    private void validateEvmRequest(TransferRequest request) {
        // 基础验证
        request.validate();

        // EVM特定验证
        String chainName = request.getChainName().toUpperCase();
        String tokenSymbol = request.getTokenSymbol().toUpperCase();

        // 验证链名称
        if (!supports(chainName)) {
            throw new IllegalArgumentException("不支持的EVM链: " + chainName +
                "，支持的链: " + SUPPORTED_CHAINS);
        }

        // 验证地址格式（EVM地址以0x开头，42个字符）
        String toAddress = request.getToAddress();
        if (!toAddress.startsWith("0x") || toAddress.length() != 42) {
            throw new IllegalArgumentException("EVM地址格式不正确，应以0x开头且长度为42个字符");
        }

        // 验证代币符号
        if (tokenSymbol.length() < 2 || tokenSymbol.length() > 10) {
            throw new IllegalArgumentException("EVM代币符号长度应在2-10个字符之间");
        }

        // 验证金额范围
        BigDecimal amount = request.getAmountAsBigDecimal();
        if (amount.compareTo(new BigDecimal("0.000000000000000001")) < 0) {
            throw new IllegalArgumentException("EVM转账金额过小，最小值为0.000000000000000001");
        }

        // 验证自定义Gas参数
        if (request.getCustomGasPrice() != null &&
            request.getCustomGasPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("自定义Gas价格必须大于0");
        }

        if (request.getCustomGasLimit() != null && request.getCustomGasLimit() <= 0) {
            throw new IllegalArgumentException("自定义Gas限制必须大于0");
        }

        log.debug("EVM转账请求验证通过: chain={}, token={}, amount={}, to={}",
            chainName, tokenSymbol, request.getAmount(), toAddress);
    }

    /**
     * 将成功结果转换为统一格式
     */
    private void convertSuccessToResult(BlockchainTransferSuccess success, BlockchainTransferResult result) {
        result.setSuccess(true);
        result.setTxHash(success.transactionHash());
        result.setFeeProvided(success.feeWalletUsed());
        result.setFeeAmount(success.feeProvided());
        result.setFeeTokenSymbol(success.getNativeTokenSymbol());
        result.setWaitedForConfirmation(true); // EvmHelper默认等待确认

        // 设置EVM特定信息
        if (success.getNativeTokenSymbol() == null && success.feeWalletUsed()) {
            // 根据链名称设置默认的手续费代币
            String chainName = result.getChainName();
            switch (chainName) {
                case "BSC":
                case "BNB":
                case "BINANCE":
                    result.setFeeTokenSymbol("BNB");
                    break;
                case "ARB":
                case "ARBITRUM":
                case "BASE":
                case "ETH":
                case "ETHEREUM":
                    result.setFeeTokenSymbol("ETH");
                    break;
                default:
                    result.setFeeTokenSymbol("ETH"); // 默认ETH
            }
        }
    }


    // ==================== 辅助方法 ====================

    /**
     * 获取Web3j连接 - 委托给EvmHelper
     */
    private Web3j configGetWeb3j(EvmConfigFacade facade) {
        return evmHelper.configGetWeb3j(facade);
    }

    /**
     * 获取原生代币符号 - 委托给EvmHelper
     */
    private String configGetNativeTokenSymbol(EvmConfigFacade facade) {
        return evmHelper.configGetNativeTokenSymbol(facade);
    }

    /**
     * 估算代币转账Gas限制 - 委托给EvmHelper
     */
    private BigInteger estimateGasForToken(Web3j web3j, String fromAddress, String contractAddress,
                                           String encodedFunction, EvmConfigFacade facade) {
        return evmHelper.transferEstimateGasForToken(web3j, fromAddress, contractAddress, encodedFunction, facade);
    }

    /**
     * 估算代币转账手续费 - 委托给EvmHelper
     */
    private EvmHelper.EvmFeeEstimate estimateTokenTransferFee(String fromAddress, String toAddress,
                                                              BigDecimal amount, String tokenSymbol, EvmConfigFacade facade) {
        return evmHelper.estimateTokenTransferFee(fromAddress, toAddress, amount, tokenSymbol, facade);
    }



    // ==================== 交易确认机制 ====================

    @Override
    protected TransactionConfirmationConfig getConfirmationConfig(TransferRequest request) {
        // EVM链确认配置：60秒超时，5秒间隔，1个确认，包含链名称信息
        return TransactionConfirmationConfig.evmDefault(request.getChainName());
    }

    protected TransactionConfirmationResult confirmTransactionInternal(String txHash, TransactionConfirmationConfig config) {
        try {
            log.debug("EVM: 开始真实交易确认 | txHash: {}, chain: {}, config: {}",
                txHash, config.getChainName(), config.getDescription());

            // 调用EvmHelper的真实确认逻辑
            TransactionConfirmationResult result = evmHelper.confirmTransaction(txHash, config);

            if (result.isSuccess()) {
                log.info("EVM: 交易确认成功 | txHash: {}, chain: {}, confirmations: {}",
                    txHash, config.getChainName(), result.getActualConfirmations());
            } else {
                log.warn("EVM: 交易确认失败 | txHash: {}, chain: {}, status: {}, error: {}",
                    txHash, config.getChainName(), result.getStatus(), result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            log.error("EVM: 交易确认异常 | txHash: {}, chain: {}, error: {}",
                txHash, config.getChainName(), e.getMessage());
            return TransactionConfirmationResult.failure(txHash,
                org.dromara.wallet.wallet.transfer.enums.TransactionStatus.UNKNOWN,
                "确认过程异常: " + e.getMessage());
        }
    }

    // ==================== 内部数据类已移至EvmHelper ====================

    @Override
    public String getDescription() {
        return "EVM兼容链转账策略，支持BSC、ARB、BASE、ETH等链的原生代币和ERC20/BEP20代币转账";
    }

    // ==================== 手续费提供业务逻辑 ====================

    /**
     * 带重试的手续费提供
     * 参考TRON模式，将业务逻辑从Helper迁移到Strategy层
     */
    private boolean provideFeeWithRetry(String userAddress, EvmConfigFacade facade, BigDecimal nativeTokenNeeded) {
        int maxRetries = 3;
        int baseDelay = 2000; // 2秒基础延迟
        String nativeSymbol = evmHelper.configGetNativeTokenSymbol(facade);

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            log.info("尝试为地址 {} 提供{}手续费，第 {}/{} 次", userAddress, nativeSymbol, attempt, maxRetries);

            try {
                boolean success = provideFeeWithVerification(userAddress, facade, nativeTokenNeeded);
                if (success) {
                    if (attempt > 1) {
                        log.info("手续费提供在第 {} 次尝试后成功", attempt);
                    }
                    return true;
                }

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxRetries) {
                    int delay = baseDelay * attempt; // 递增延迟：2秒、4秒、6秒
                    log.warn("第 {} 次提供手续费失败，{}秒后重试", attempt, delay / 1000);
                    Thread.sleep(delay);
                }

            } catch (Exception e) {
                log.warn("第 {} 次提供手续费异常: {}", attempt, e.getMessage());

                // 判断是否应该重试
                if (shouldRetryFeeProvision(e) && attempt < maxRetries) {
                    int delay = baseDelay * attempt;
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    break;
                }
            }
        }

        log.error("手续费提供在 {} 次尝试后仍然失败", maxRetries);
        return false;
    }

    /**
     * 带验证的手续费提供
     * 核心业务逻辑：检查配置、计算差额、执行转账、验证结果
     */
    private boolean provideFeeWithVerification(String userAddress, EvmConfigFacade facade, BigDecimal nativeTokenNeeded) {
        String nativeSymbol = evmHelper.configGetNativeTokenSymbol(facade);
        log.info("为地址提供{}手续费（带验证）: address={}, 需要总额={}", nativeSymbol, userAddress, nativeTokenNeeded);

        try {
            // 1. 检查手续费钱包是否启用
            if (!facade.isFeeWalletEnabled()) {
                log.warn("{}手续费钱包未启用", facade.getChainName());
                return false;
            }

            String feeWalletAddress = facade.getFeeWalletAddress();
            String feeWalletPrivateKey = facade.getFeeWalletPrivateKey();
            if (feeWalletAddress == null || feeWalletPrivateKey == null) {
                log.warn("{}手续费钱包配置未找到", facade.getChainName());
                return false;
            }

            // 2. 检查用户当前余额，计算需要补给的差额
            BigDecimal userCurrentBalance = evmHelper.balanceGetNativeForRead(userAddress, facade);
            BigDecimal actualNeeded = nativeTokenNeeded.subtract(userCurrentBalance);

            if (actualNeeded.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("用户{}余额已足够，无需补给: {} >= {}", nativeSymbol, userCurrentBalance, nativeTokenNeeded);
                return true; // 用户余额已足够，无需补给
            }

            log.info("计算需要补给的{}差额: {}", nativeSymbol, actualNeeded);

            // 3. 检查手续费钱包余额是否足够补给差额
            BigDecimal feeWalletBalance = evmHelper.balanceGetNativeForRead(feeWalletAddress, facade);
            if (feeWalletBalance.compareTo(actualNeeded) < 0) {
                log.warn("手续费钱包{}余额不足以补给差额: {} < {}", nativeSymbol, feeWalletBalance, actualNeeded);
                return false;
            }

            // 4. 从手续费钱包转账差额到用户地址
            String txHash = transferNative(feeWalletAddress, feeWalletPrivateKey, actualNeeded, userAddress, facade);
            log.info("手续费补给转账已提交: 差额={} {}, txHash={}", actualNeeded, nativeSymbol, txHash);

            // 5. 快速验证转账是否成功（等待交易上链即可）
            boolean verified = evmHelper.quickStatusCheck(txHash, facade); // 15秒超时

            if (verified) {
                // 6. 再次检查用户地址的原生代币余额是否足够
                BigDecimal userFinalBalance = evmHelper.balanceGetNativeForRead(userAddress, facade);
                if (userFinalBalance.compareTo(nativeTokenNeeded) >= 0) {
                    log.info("成功为地址 {} 补给 {} {} 手续费: 补给前={}, 补给后={}, 需要={}",
                        userAddress, actualNeeded, nativeSymbol, userCurrentBalance, userFinalBalance, nativeTokenNeeded);
                    return true; // 确认手续费已到账
                } else {
                    log.warn("手续费转账验证成功但用户余额仍不足: 补给后={} < 需要={}", userFinalBalance, nativeTokenNeeded);
                }
            } else {
                log.warn("手续费转账验证失败: txHash={}", txHash);
            }

            return false;

        } catch (Exception e) {
            log.error("提供{}手续费（带验证）失败", nativeSymbol, e);
            return false;
        }
    }

    /**
     * 判断是否应该重试手续费提供
     */
    private boolean shouldRetryFeeProvision(Exception e) {
        String message = e.getMessage();
        if (message == null) return true;

        // 不可重试的错误类型
        String[] nonRetryableErrors = {
            "insufficient funds",
            "balance too low",
            "nonce too low",
            "replacement transaction underpriced",
            "already known"
        };

        String lowerMessage = message.toLowerCase();
        for (String error : nonRetryableErrors) {
            if (lowerMessage.contains(error)) {
                return false;
            }
        }

        return true; // 默认可重试
    }

    // ==================== 增强错误处理方法 ====================

    /**
     * 带重试的手续费处理
     */
    private FeeProvisionResult handleFeeProvisionWithRetry(TransferRequest request) {
        int maxRetries = 2;
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                return handleFeeProvision(request);
            } catch (Exception e) {
                lastException = e;
                log.warn("{}手续费处理失败，第{}次尝试: {}", request.getChainName(), attempt, e.getMessage());

                if (attempt < maxRetries) {
                    // 等待后重试
                    try {
                        Thread.sleep(1000 * attempt); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        // 所有重试都失败，抛出EVM特定异常
        throw EvmTransferException.fromGenericException(request.getChainName(), lastException, null);
    }

    /**
     * 带重试的转账执行
     */
    private String executeTransferWithRetry(TransferRequest request, EvmConfigFacade facade, boolean isNativeToken) {
        int maxRetries = getMaxRetriesForTransfer(request.getChainName());
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                if (isNativeToken) {
                    return executeNativeTransfer(request, facade);
                } else {
                    return executeTokenTransfer(request, facade);
                }
            } catch (Exception e) {
                lastException = e;
                EvmTransferException evmException = convertToEvmException(e, request.getChainName(), null);

                log.warn("{}转账执行失败，第{}次尝试: type={}, error={}",
                        request.getChainName(), attempt, evmException.getErrorType().getDescription(), e.getMessage());

                // 检查是否可重试
                if (!evmException.isRetryable() || attempt >= maxRetries) {
                    break;
                }

                // 等待后重试
                try {
                    Thread.sleep(2000 * attempt); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 所有重试都失败，抛出EVM特定异常
        throw convertToEvmException(lastException, request.getChainName(), null);
    }

    /**
     * 转账异常处理
     */
    private void handleTransferException(Exception e, String chainName, String txHash, long transferTime, TransferRequest request) {
        // 转换为EVM特定异常
        EvmTransferException evmException = convertToEvmException(e, chainName, txHash);

        // 记录异常指标
        recordTransferFailure(chainName, evmException.getErrorType().name(), transferTime);

        // 发送告警
        sendExceptionAlert(evmException, txHash, buildExceptionContext(request));

        log.error("{}转账异常处理: type={}, txHash={}, time={}ms, retryable={}, advice={}",
                chainName, evmException.getErrorType().getDescription(), txHash, transferTime,
                evmException.isRetryable(), evmException.getRecoveryAdvice());
    }

    /**
     * 转换为EVM特定异常
     */
    private EvmTransferException convertToEvmException(Exception e, String chainName, String txHash) {
        if (e instanceof EvmTransferException) {
            return (EvmTransferException) e;
        }
        return EvmTransferException.fromGenericException(chainName, e, txHash);
    }

    /**
     * 构建异常上下文信息
     */
    private String buildExceptionContext(TransferRequest request) {
        return String.format("Chain=%s, Token=%s, Amount=%s, To=%s",
                request.getChainName(), request.getTokenSymbol(),
                request.getAmount(), request.getToAddress());
    }

    /**
     * 获取转账最大重试次数
     */
    private int getMaxRetriesForTransfer(String chainName) {
        // 可以根据不同链配置不同的重试次数
        return 2; // 默认重试2次
    }

    // ==================== 监控指标记录方法 ====================

    /**
     * 记录转账开始
     */
    private void recordTransferStart(String chainName, String transferType) {
        if (transferMetrics != null && isMonitoringEnabled()) {
            transferMetrics.recordTransferStart(chainName, transferType);
        }
    }

    /**
     * 记录转账成功
     */
    private void recordTransferSuccess(String chainName, long transferTime, long confirmationTime,
                                     BigDecimal gasAmount, boolean gasProvided) {
        if (transferMetrics != null && isMonitoringEnabled()) {
            transferMetrics.recordTransferSuccess(chainName, transferTime, confirmationTime, gasAmount, gasProvided);
        }
    }

    /**
     * 记录转账失败
     */
    private void recordTransferFailure(String chainName, String exceptionType, long transferTime) {
        if (transferMetrics != null && isMonitoringEnabled()) {
            transferMetrics.recordTransferFailure(chainName, exceptionType, transferTime);
        }
    }

    /**
     * 发送异常告警
     */
    private void sendExceptionAlert(EvmTransferException exception, String txHash, String context) {
        if (alertService != null && isAlertingEnabled()) {
            // 将EVM异常转换为通用告警格式
            alertService.sendCustomAlert(
                exception.getAlertLevel(),
                exception.getChainName() + "转账异常告警",
                exception.toString(),
                context
            );
        }
    }

    /**
     * 检查是否启用监控
     */
    private boolean isMonitoringEnabled() {
        return transferMetrics != null;
    }

    /**
     * 检查是否启用告警
     */
    private boolean isAlertingEnabled() {
        return alertService != null && isMonitoringEnabled();
    }

    /**
     * 生成监控报告
     */
    public EvmTransferMonitorReport generateMonitorReport() {
        if (transferMetrics != null && isMonitoringEnabled()) {
            return transferMetrics.generateReport();
        }
        return null;
    }

    /**
     * 检查并发送性能告警
     */
    public void checkAndSendPerformanceAlert() {
        if (transferMetrics != null && alertService != null && isMonitoringEnabled()) {
            EvmTransferMonitorReport report = transferMetrics.generateReport();
            if (report != null && report.needsAlert()) {
                alertService.sendCustomAlert(
                    report.getAlertLevel(),
                    "EVM转账性能告警",
                    report.generateAlertReport(),
                    "EVM多链转账性能监控"
                );
            }
        }
    }
}
